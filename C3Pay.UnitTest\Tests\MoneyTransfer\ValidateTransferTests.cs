using C3Pay.Core.Models.DTOs.MoneyTransfer;
using C3Pay.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using System;
using System.Threading.Tasks;
using C3Pay.Core;
using C3Pay.Core.Models;
using static C3Pay.Core.BaseEnums;
using FluentAssertions;
using Microsoft.Extensions.Caching.Distributed;

namespace C3Pay.UnitTest.Tests.MoneyTransfer
{
    public class ValidateTransferTests
    {
        private readonly Mock<ILogger<MoneyTransferService>> _mockLogger;
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IDistributedCache> _mockCacheService;

        public ValidateTransferTests()
        {
            _mockLogger = new Mock<ILogger<MoneyTransferService>>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockCacheService = new Mock<IDistributedCache>();
        }

        [Fact]
        public void ReviewTransferDto_ShouldHaveNewProperties()
        {
            // Arrange & Act
            var reviewTransferDto = new ReviewTransferDto();

            // Assert
            reviewTransferDto.Should().NotBeNull();
            reviewTransferDto.GetType().Should().HaveProperty<bool>("IsInValidSendAmountError");
            reviewTransferDto.GetType().Should().HaveProperty<InValidSendAmountError>("InValidSendAmountError");
        }

        [Fact]
        public void InValidSendAmountError_ShouldHaveCorrectProperties()
        {
            // Arrange & Act
            var invalidAmountError = new InValidSendAmountError
            {
                ErrorType = ErrorType.Warning.ToString(),
                Title = "Try Smaller Amount",
                Description = "You can send upto AED 864.25 due to a small fee",
                PrimaryCta = new PrimaryCta { Text = "Send 864.25" },
                SecondaryCta = new SecondaryCta { Text = "Cancel" },
                SuggestedAmountToSend = 864.25m
            };

            // Assert
            invalidAmountError.ErrorType.Should().Be("Warning");
            invalidAmountError.Title.Should().Be("Try Smaller Amount");
            invalidAmountError.Description.Should().Be("You can send upto AED 864.25 due to a small fee");
            invalidAmountError.PrimaryCta.Text.Should().Be("Send 864.25");
            invalidAmountError.SecondaryCta.Text.Should().Be("Cancel");
            invalidAmountError.SuggestedAmountToSend.Should().Be(864.25m);
        }

        [Fact]
        public void ErrorType_Enum_ShouldHaveCorrectValues()
        {
            // Assert
            ErrorType.Warning.ToString().Should().Be("Warning");
            ErrorType.Error.ToString().Should().Be("Error");
        }

        [Fact]
        public void ReviewTransferDto_ShouldInitializeWithDefaultValues()
        {
            // Arrange & Act
            var reviewTransferDto = new ReviewTransferDto();

            // Assert
            reviewTransferDto.IsInValidSendAmountError.Should().BeFalse();
            reviewTransferDto.InValidSendAmountError.Should().BeNull();
        }

        [Fact]
        public void ReviewTransferDto_ShouldSetInvalidAmountErrorCorrectly()
        {
            // Arrange
            var reviewTransferDto = new ReviewTransferDto();
            var invalidAmountError = new InValidSendAmountError
            {
                ErrorType = ErrorType.Warning.ToString(),
                Title = "Test Title",
                Description = "Test Description",
                SuggestedAmountToSend = 100.50m
            };

            // Act
            reviewTransferDto.IsInValidSendAmountError = true;
            reviewTransferDto.InValidSendAmountError = invalidAmountError;

            // Assert
            reviewTransferDto.IsInValidSendAmountError.Should().BeTrue();
            reviewTransferDto.InValidSendAmountError.Should().NotBeNull();
            reviewTransferDto.InValidSendAmountError.ErrorType.Should().Be("Warning");
            reviewTransferDto.InValidSendAmountError.SuggestedAmountToSend.Should().Be(100.50m);
        }
    }
}
