using C3Pay.Core.Models.DTOs.MoneyTransfer;
using C3Pay.Services;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using System;
using System.Threading.Tasks;
using C3Pay.Core;
using C3Pay.Core.Models;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.UnitTest.Tests.MoneyTransfer
{
    [TestFixture]
    public class ValidateTransferTests
    {
        private Mock<ILogger<MoneyTransferService>> _mockLogger;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IDistributedCache> _mockCacheService;

        [SetUp]
        public void Setup()
        {
            _mockLogger = new Mock<ILogger<MoneyTransferService>>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockCacheService = new Mock<IDistributedCache>();
        }

        [Test]
        public void ReviewTransferDto_ShouldHaveNewProperties()
        {
            // Arrange & Act
            var reviewTransferDto = new ReviewTransferDto();

            // Assert
            Assert.That(reviewTransferDto, Has.Property("IsInValidSendAmountError"));
            Assert.That(reviewTransferDto, Has.Property("InValidSendAmountError"));
        }

        [Test]
        public void InValidSendAmountError_ShouldHaveCorrectProperties()
        {
            // Arrange & Act
            var invalidAmountError = new InValidSendAmountError
            {
                ErrorType = ErrorType.Warning.ToString(),
                Title = "Try Smaller Amount",
                Description = "You can send upto AED 864.25 due to a small fee",
                PrimaryCta = new PrimaryCta { Text = "Send 864.25" },
                SecondaryCta = new SecondaryCta { Text = "Cancel" },
                SuggestedAmountToSend = 864.25m
            };

            // Assert
            Assert.That(invalidAmountError.ErrorType, Is.EqualTo("Warning"));
            Assert.That(invalidAmountError.Title, Is.EqualTo("Try Smaller Amount"));
            Assert.That(invalidAmountError.Description, Is.EqualTo("You can send upto AED 864.25 due to a small fee"));
            Assert.That(invalidAmountError.PrimaryCta.Text, Is.EqualTo("Send 864.25"));
            Assert.That(invalidAmountError.SecondaryCta.Text, Is.EqualTo("Cancel"));
            Assert.That(invalidAmountError.SuggestedAmountToSend, Is.EqualTo(864.25m));
        }

        [Test]
        public void ErrorType_Enum_ShouldHaveCorrectValues()
        {
            // Assert
            Assert.That(ErrorType.Warning.ToString(), Is.EqualTo("Warning"));
            Assert.That(ErrorType.Error.ToString(), Is.EqualTo("Error"));
        }

        [Test]
        public void ReviewTransferDto_ShouldInitializeWithDefaultValues()
        {
            // Arrange & Act
            var reviewTransferDto = new ReviewTransferDto();

            // Assert
            Assert.That(reviewTransferDto.IsInValidSendAmountError, Is.False);
            Assert.That(reviewTransferDto.InValidSendAmountError, Is.Null);
        }

        [Test]
        public void ReviewTransferDto_ShouldSetInvalidAmountErrorCorrectly()
        {
            // Arrange
            var reviewTransferDto = new ReviewTransferDto();
            var invalidAmountError = new InValidSendAmountError
            {
                ErrorType = ErrorType.Warning.ToString(),
                Title = "Test Title",
                Description = "Test Description",
                SuggestedAmountToSend = 100.50m
            };

            // Act
            reviewTransferDto.IsInValidSendAmountError = true;
            reviewTransferDto.InValidSendAmountError = invalidAmountError;

            // Assert
            Assert.That(reviewTransferDto.IsInValidSendAmountError, Is.True);
            Assert.That(reviewTransferDto.InValidSendAmountError, Is.Not.Null);
            Assert.That(reviewTransferDto.InValidSendAmountError.ErrorType, Is.EqualTo("Warning"));
            Assert.That(reviewTransferDto.InValidSendAmountError.SuggestedAmountToSend, Is.EqualTo(100.50m));
        }
    }
}
