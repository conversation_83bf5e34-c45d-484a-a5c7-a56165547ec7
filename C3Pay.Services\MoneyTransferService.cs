using AutoMapper;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using C3Pay.Core.Models.DTOs.MoneyTransfer;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Messages.MoneyTransfer;
using C3Pay.Core.Models.Portal;
using C3Pay.Core.Models.Settings;
using C3Pay.Core.Models.Structs;
using C3Pay.Core.Services;
using C3Pay.Services.DTOs;
using C3Pay.Services.Filters;
using C3Pay.Services.Helper;
using C3Pay.Services.Mapping;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Beneficiaries;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Rates;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Transfers;
using Edenred.Common.Core.Models.Messages.Integration.Transactions;
using Edenred.Common.Core.Models.Settings.Integration.MoneyTransfer.RakBank;
using Edenred.Common.Core.Services.Integration.MoneyTransfer;
using Edenred.Common.Services;
using FluentValidation;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;
using static Edenred.Common.Core.Enums;
using BeneficiaryAdditionalField = C3Pay.Core.Models.C3Pay.MoneyTransfer.BeneficiaryAdditionalField;
using ExternalProviderBeneficiaryAdditionalField = Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Beneficiaries.BeneficiaryAdditionalField;
using MobileApplicationId = C3Pay.Core.BaseEnums.MobileApplicationId;
using MoneyTransferMethodType = C3Pay.Core.MoneyTransferMethodType;

namespace C3Pay.Services
{
    /// <summary>
    /// 
    /// </summary>
    public class MoneyTransferService : IMoneyTransferService
    {
        private readonly Status[] nonUpdatableStatuses = new Status[] { Status.REVERSED, Status.PENDINGREVERSE, Status.FAILEDTOREVERSE, Status.NEEDSMANUALREVERSAL };
        private readonly IMoneyTransferBeneficiaryService _moneyTransferBeneficiaryService;
        private readonly ReferralProgramServiceSettings _referralProgramServiceSettings;
        private readonly ISalaryAdvanceCashBackService _salaryAdvanceCashBackService;
        private readonly MoneyTransferServiceSettings _moneyTransferServiceSettings;
        private readonly IPushNotificationSenderService _pushNotificationService;
        private readonly IAnalyticsPublisherService _analyticsPublisherService;
        private readonly ITextMessageSenderService _textMessageSenderService;
        private readonly IReferralProgramService _referralProgramService;
        private readonly IMessagingQueueService _messagingQueueService;
        private readonly IUnitOfWorkReadOnly _unitOfWorkReadOnly;
        private readonly IPPSWebAuthService _ppsWebAuthService;
        private readonly RAKSettings _rakSettings;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IRAKService _rakService;
        private readonly ILogger _logger;
        private readonly ITransactionsB2CService _transactionsB2CService;
        private readonly IPartnerCorporateService _partnerCorporateService;
        private readonly ILookupService _lookupService;
        private readonly IDistributedCache _cacheService;
        private readonly IMapper _mapper;
        private readonly IIdentityService _identityService;
        private readonly IMapper _mapperGeneral;
        private readonly IFeatureManager _featureManager;
        private readonly IValidator<MoneyTransferSuspiciousInformation> _validator;
        private readonly IExternalProviderMoneyTransferService _externalProviderMoneyTransferService;
        private readonly IAuditTrailService _auditTrailService;
        private readonly RakBankMoneyTransferSettings _externalProviderMoneyTransferSettings;
        private readonly GeneralSettings generalSettings;
        private readonly IRewardService _rewardService;
        private readonly RewardServiceSettings _rewardServiceSettings;
        private readonly IConfiguration _configuration;

        private readonly decimal _maxNpReceiveAmount = 100000;
        private readonly decimal _maxPkReceiveAmount = 500000;
        private readonly decimal _maxPhReceiveAmount = 50000;

        private readonly string _remittanceDestinationCacheKey = "C3_C_RemittanceDestinations";
        private readonly string _fieldGroupsCacheKey = "C3_C_FieldGroups";
        private readonly string _moneyTransferTextLocalizationCacheKey = "C3_C_MoneyTransferTextLocalization";
        private readonly string _repeatTransferCacheKeyPrefix = "C3_RepeatTransfer_";
        private readonly string _beneficiaryListCacheKeyPrefix = "C3_BeneficiaryList_";
        private readonly string _lookupCachePrefix = "LookupTemp_";

        private Guid ExistingBeneficiaryId { get; set; }

        private static readonly string[] reversalsErrorMessages = new string[]
        {
            "20010-Beneficiary Bank currently Not Supported.",
            "20011-Restricted  Customer or Beneficiary Nationality",
            "422010-beneficiary_id not found",
            "422010-customer_id not found",
            "20006-The transaction failed due to incorrect address",
            "404001-Validate REST",
            "20005-TRANSACTION AMOUNT MUST NOT EXCEED",
            "Failed to initialize RAK Bank service"
        };

        private static readonly string[] reversalsExceptionMessages = new string[]
        {
            "Rak Access token error",
            "Unexpected character encountered while parsing value",
            "No such host is known",
            "Error reading JObject from JsonReader",
            "The SSL connection could not be established",
            "No connection could be made because the target machine actively refused it",
            "A connection attempt failed",
            "An error occurred while sending the request",
            "The operation was canceled",
            "Value cannot be null"
        };

        private static readonly string[] beneficiaryRetryMessages = new string[]
        {
            "404001-Validate REST",
            "500-Sorry, we are unable to process your transaction. Please try again.",
            "Error reading JObject from JsonReader",
            "Rak Access token error",
            "The SSL connection could not be established",
            "Internal Server Error",
            "Failed to initialize RAK Bank service",
            "A connection attempt failed"
        };

        private static readonly string[] allowRemarksForReversal = new string[]
        {
            "SUSPICIOUS TRANSACTION REJECTED"
        };

        /// <summary>
        /// Money Transfer Service
        /// </summary>
        /// <param name="referralProgramServiceSettings"></param>
        /// <param name="moneyTransferServiceSettings"></param>
        /// <param name="moneyTransferBeneficiaryService"></param>
        /// <param name="pushNotificationService"></param>
        /// <param name="analyticsPublisherService"></param>
        /// <param name="textMessageSenderService"></param>
        /// <param name="referralProgramService"></param>
        /// <param name="messagingQueueService"></param>
        /// <param name="unitOfWorkReadOnly"></param>
        /// <param name="logger"></param>
        /// <param name="rakSettings"></param>
        /// <param name="ppsWebAuthService"></param>
        /// <param name="unitOfWork"></param>
        /// <param name="rakService"></param>
        /// <param name="lookupService"></param>
        /// <param name="featureManager"></param>
        /// <param name="auditTrailService"></param>
        public MoneyTransferService(IOptions<ReferralProgramServiceSettings> referralProgramServiceSettings,
                                    IOptions<MoneyTransferServiceSettings> moneyTransferServiceSettings,
                                    IMoneyTransferBeneficiaryService moneyTransferBeneficiaryService,
                                    ISalaryAdvanceCashBackService salaryAdvanceCashBackService,
                                    IPushNotificationSenderService pushNotificationService,
                                    IAnalyticsPublisherService analyticsPublisherService,
                                    ITextMessageSenderService textMessageSenderService,
                                    IReferralProgramService referralProgramService,
                                    IMessagingQueueService messagingQueueService,
                                    IUnitOfWorkReadOnly unitOfWorkReadOnly,
                                    ILogger<MoneyTransferService> logger,
                                    IOptions<RAKSettings> rakSettings,
                                    IPPSWebAuthService ppsWebAuthService,
                                    IUnitOfWork unitOfWork,
                                    IRAKService rakService,
                                    ITransactionsB2CService transactionsB2CService,
                                    IPartnerCorporateService partnerCorporateService,
                                    ILookupService lookupService,
                                    IDistributedCache cacheService,
                                    IMapper mapper,
                                    IIdentityService identityService,
                                    IFeatureManager featureManager,
                                    IValidator<MoneyTransferSuspiciousInformation> validator,
                                    IExternalProviderMoneyTransferService externalProviderMoneyTransferService,
                                    IAuditTrailService auditTrailService,
                                    IOptions<RakBankMoneyTransferSettings> externalProviderMoneyTransferSettings,
                                    IOptions<GeneralSettings> _generalSettings,
                                    IRewardService rewardService,
                                    IOptions<RewardServiceSettings> rewardServiceSettings,
                                    IConfiguration configuration)
        {
            _referralProgramServiceSettings = referralProgramServiceSettings.Value;
            _moneyTransferBeneficiaryService = moneyTransferBeneficiaryService;
            _moneyTransferServiceSettings = moneyTransferServiceSettings.Value;
            _salaryAdvanceCashBackService = salaryAdvanceCashBackService;
            _analyticsPublisherService = analyticsPublisherService;
            _textMessageSenderService = textMessageSenderService;
            _pushNotificationService = pushNotificationService;
            _referralProgramService = referralProgramService;
            _messagingQueueService = messagingQueueService;
            _unitOfWorkReadOnly = unitOfWorkReadOnly;
            _ppsWebAuthService = ppsWebAuthService;
            _rakSettings = rakSettings.Value;
            _unitOfWork = unitOfWork;
            _rakService = rakService;
            _logger = logger;
            _transactionsB2CService = transactionsB2CService;
            _partnerCorporateService = partnerCorporateService;
            _lookupService = lookupService;
            _cacheService = cacheService;
            var mapperConfiguration = new MapperConfiguration(configuration => configuration.AddProfile(new MoneyTransferMappingProfile()));
            _mapper = mapperConfiguration.CreateMapper();
            _identityService = identityService;
            _auditTrailService = auditTrailService;
            _mapperGeneral = mapper;
            _featureManager = featureManager;
            _validator = validator;
            _externalProviderMoneyTransferService = externalProviderMoneyTransferService;
            _externalProviderMoneyTransferSettings = externalProviderMoneyTransferSettings.Value;
            this.generalSettings = _generalSettings.Value;
            _rewardService = rewardService;
            _rewardServiceSettings = rewardServiceSettings.Value;
            _configuration = configuration;
        }

        public async Task<ServiceResponse<BankDetailsRakModel>> GetBankBranchLists(Guid userId, string countryCode, string bankName, string bankBranchName)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == userId, z => z.CardHolder);

            if (user == null)
            {
                _logger.LogWarning($"Due to invalid user id, Fx rates failed to get from RAK");

                return new ServiceResponse<BankDetailsRakModel>(false, BaseEnums.TransferStatusValidationMessage.UserNotExists.ToString());
            }

            var emiratesId = user.CardHolder.EmiratesId;

            var bankParameters = new List<BankRequestFiledsRakModel>
            {
                new BankRequestFiledsRakModel
                {
                    FieldId = "other_bank_name",
                    FieldName = bankName
                }
            };

            if (!string.IsNullOrEmpty(bankBranchName))
            {
                bankParameters.Add(new BankRequestFiledsRakModel
                {
                    FieldId = "other_branch_name",
                    FieldName = bankBranchName,
                });
            }

            var getBanksRequest = new BanksRequestRakModel()
            {
                Country = countryCode.ToUpper(),
                BankRequestFields = bankParameters,
            };

            return await this._rakService.GetBanksDetails(getBanksRequest, emiratesId);
        }

        public async Task<ServiceResponse<BankDetailsRakModel>> GetBankBranchDetailsForIFSCCode(Guid userId, string ifscCode)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == userId, z => z.CardHolder);

            if (user == null)
            {
                _logger.LogWarning($"Due to invalid user id, Fx rates failed to get from RAK");
                return new ServiceResponse<BankDetailsRakModel>(false, BaseEnums.TransferStatusValidationMessage.UserNotExists.ToString());
            }

            var emiratesId = user.CardHolder.EmiratesId;

            var bankParameters = new List<BankRequestFiledsRakModel>();

            bankParameters.Add(new BankRequestFiledsRakModel
            {
                FieldId = "identifier_code1",
                FieldName = ifscCode.ToUpper(),
            });

            var getBanksRequest = new BanksRequestRakModel()
            {
                Country = BaseEnums.CountryEnable.IN.ToString(),
                BankRequestFields = bankParameters,
            };

            return await this._rakService.GetBanksDetails(getBanksRequest, emiratesId);
        }

        public async Task<ServiceResponse<FxRateResponseRakModel>> GetFxRates(Guid userId, string toCurrency, decimal amount, string transferMethod, string beneficiaryId = null)
        {
            var rateExpiryInMinutes = _moneyTransferServiceSettings.RateExpiryInMinutes;
            var hasRateChanged = false;

            // User validations.
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == userId, i => i.CardHolder);
            if (user == null)
            {
                return new ServiceResponse<FxRateResponseRakModel>(false, TransferStatusValidationMessage.UserNotExists.ToString());
            }

            // Amount validations.
            if (amount <= 0)
            {
                return new ServiceResponse<FxRateResponseRakModel>(false, TransferStatusValidationMessage.AmountNotExists.ToString());
            }

            // Currency validations.
            if (string.IsNullOrEmpty(toCurrency) || toCurrency.ToUpper() == "AED")
            {
                return new ServiceResponse<FxRateResponseRakModel>(false, TransferStatusValidationMessage.InvalidToCurrency.ToString());
            }
            else
            {
                toCurrency = toCurrency.Trim().ToUpper();
            }

            // Transfer method validation.
            TransferMethod moneyTransferType = TransferMethod.BANKTRANSFER;
            if (string.IsNullOrWhiteSpace(transferMethod))
            {
                // Default transfer type to be bank transfer.
                moneyTransferType = TransferMethod.BANKTRANSFER;
            }
            else if (Enum.TryParse(transferMethod, true, out moneyTransferType) == false || moneyTransferType == TransferMethod.DIRECTTRANSFER)
            {
                return new ServiceResponse<FxRateResponseRakModel>(false, TransferStatusValidationMessage.TransferMethodNotExists.ToString());
            }

            var rates = new FxRateConversionResponseRakModel();
            var countryCode = "";

            // Try find provider.
            MoneyTransferProvider provider;
            if (moneyTransferType == TransferMethod.BANKTRANSFER)
            {
                provider = await _unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.Currency.ToUpper() == toCurrency && x.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer);
            }
            else if (moneyTransferType == TransferMethod.CASHPICKUP)
            {
                provider = await _unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.Currency.ToUpper() == toCurrency && x.MoneyTransferMethodType == MoneyTransferMethodType.CashPickup);
            }
            else if (moneyTransferType == TransferMethod.WALLET)
            {
                provider = await _unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.Currency.ToUpper() == toCurrency && x.MoneyTransferMethodType == MoneyTransferMethodType.Wallet);
            }
            else
            {
                provider = null;
            }

            if (provider == null || provider.WUGetRatesEnabled == false)
            {
                // Fall back to original code.
                var country = await _unitOfWork.Countries.FirstOrDefaultAsync(z => z.Currency == toCurrency.ToUpper());
                if (country == null)
                {
                    return new ServiceResponse<FxRateResponseRakModel>(false, TransferStatusValidationMessage.InvalidToCurrency.ToString());
                }

                countryCode = country.Code;

                var getBankTransferRatesRequest = new FxRateRequestRakModel()
                {
                    TransactionType = TransactionType.RAKMoney.ToString(),
                    FromCurrency = ConstantParam.DefaultCurrency,
                    ToCurrency = toCurrency.ToUpper(),
                    Fxvalue = new FxValueRakModel()
                    {
                        Amount = amount.ToString(),
                        Currency = ConstantParam.DefaultCurrency.ToUpper()
                    },
                    BankCountry = country.Code,
                    BeneficiaryId = beneficiaryId,
                    Charges = true,
                };

                if (country.EligibleForBankTransfer && moneyTransferType == TransferMethod.BANKTRANSFER)
                {
                    getBankTransferRatesRequest.TransferType = MoneyTransferType.OutsideUAE.ToString();
                }

                if (country.EligibleForCashPickUp && moneyTransferType == TransferMethod.CASHPICKUP)
                {
                    getBankTransferRatesRequest.TransferType = MoneyTransferType.RAKMoneyCashPayout.ToString();
                }

                var emiratesId = user.CardHolder.EmiratesId;

                var ratesResult = await this._rakService.GetFxRate(new List<FxRateRequestRakModel>() { getBankTransferRatesRequest }, emiratesId);
                if (!ratesResult.IsSuccessful)
                {
                    return new ServiceResponse<FxRateResponseRakModel>(false, TransferStatusValidationMessage.FxRateNotFoundFromRak.ToString());
                }

                rates = ratesResult.Data.First();

                var convertedRate = Convert.ToDecimal(rates.FxConversionRates.First().Rate);
                if (country.EligibleForBankTransfer && moneyTransferType == TransferMethod.BANKTRANSFER && country.BankTransferLatestRate != convertedRate)
                {
                    hasRateChanged = true;
                    country.BankTransferLatestRate = convertedRate;
                }

                if (country.EligibleForCashPickUp && moneyTransferType == TransferMethod.CASHPICKUP && country.CashPickUpLatestRate != convertedRate)
                {
                    hasRateChanged = true;
                    country.CashPickUpLatestRate = convertedRate;
                }

                country.RatesLastUpdatedDate = DateTime.Now.AddMinutes(rateExpiryInMinutes);
            }
            else
            {
                // Use WU integration.
                countryCode = provider.CountryCode;

                var request = new GetFxRateRequest()
                {
                    EmiratesId = user.CardHolder.EmiratesId,

                    BaseCurrency = "AED",
                    TargetCurrency = toCurrency,

                    ExchangeAmount = new ExchangeAmount()
                    {
                        Amount = amount.ToString(),
                        Currency = "AED"
                    },

                    BankCountryCode = provider.CountryCode,
                    CalculateCharges = true
                };

                if (moneyTransferType == TransferMethod.BANKTRANSFER)
                {
                    request.MoneyTransferMethodType = Enums.MoneyTransferMethodType.BankTransfer;
                }
                else if (moneyTransferType == TransferMethod.CASHPICKUP)
                {
                    request.MoneyTransferMethodType = Enums.MoneyTransferMethodType.CashPickup;
                }
                else if (moneyTransferType == TransferMethod.WALLET)
                {
                    request.MoneyTransferMethodType = Enums.MoneyTransferMethodType.Wallet;
                }

                var result = await this._externalProviderMoneyTransferService.GetFxRate(request);
                if (result.IsSuccessful == false)
                {
                    return new ServiceResponse<FxRateResponseRakModel>(false, TransferStatusValidationMessage.FxRateNotFoundFromRak.ToString());
                }

                rates = _mapper.Map<FxRateConversionResponseRakModel>(result.Data);

                var convertedRate = Math.Round(1 / Convert.ToDecimal(rates.FxConversionRates.First().Rate), 5);
                if (provider.Rate != convertedRate)
                {
                    hasRateChanged = true;
                    provider.Rate = convertedRate;
                }

                provider.RateExpiryDate = DateTime.Now.AddMinutes(rateExpiryInMinutes);
            }

            await _unitOfWork.CommitAsync();

            if (hasRateChanged)
            {
                await ClearCache(_remittanceDestinationCacheKey);
            }

            if (moneyTransferType == TransferMethod.BANKTRANSFER)
            {
                rates.TransferType = MoneyTransferType.OutsideUAE.ToString();
            }
            else if (moneyTransferType == TransferMethod.CASHPICKUP)
            {
                rates.TransferType = MoneyTransferType.RAKMoneyCashPayout.ToString();
            }
            else if (moneyTransferType == TransferMethod.WALLET)
            {
                rates.TransferType = MoneyTransferType.Wallet.ToString();
            }
            var disableLoyaltyWaive = await _featureManager.IsEnabledAsync(FeatureFlags.Mt_DisableLoyaltyWaive);
            var freeTransferDetails = await this._unitOfWork.MoneyTransferTransactions.GetFreeTransferDetails(userId,
                                                                                                              _rakSettings.LoyaltyLimitAmount,
                                                                                                              Convert.ToDateTime(_rakSettings.LoyaltyImplementDate),
                                                                                                              _rakSettings.LoyaltyLimitCount,
                                                                                                              disableLoyaltyWaive);

            ServiceResponse<decimal?> transferFees = null;
            if (transferMethod != null)
            {
                transferFees = await _lookupService.GetTransferFees(countryCode, moneyTransferType, amount, userId);
            }

            if (rates.ConversionCharges != null)
            {
                if (transferFees != null && transferFees.Data.HasValue)
                {
                    // Subtracting Rakbank's transfer fees and adding C3Pay's transfer fees to get the total debit amount
                    rates.TotalDebitAmount = (TypeUtility.GetDoubleFromString(rates.TotalDebitAmount) - TypeUtility.GetDoubleFromString(rates.ConversionCharges.TotalCharges) + Convert.ToDouble(transferFees.Data.Value)).ToString();
                    rates.ConversionCharges.Amount = transferFees.Data.ToString();
                    rates.ConversionCharges.TotalCharges = transferFees.Data.ToString();
                }

                if (freeTransferDetails.FreeFirstTransfer || freeTransferDetails.FreeLoyaltyTransfer)
                {
                    // Removing transfer fees because of waiver to get the total debit amount
                    rates.TotalDebitAmount = (TypeUtility.GetDoubleFromString(rates.TotalDebitAmount) - TypeUtility.GetDoubleFromString(rates.ConversionCharges.TotalCharges)).ToString();
                    rates.ConversionCharges.TotalCharges = "0";
                }
            }

            var response = new FxRateResponseRakModel()
            {
                FxRates = new List<FxRateConversionResponseRakModel>() { rates },
                IsFirstTransfer = freeTransferDetails.FreeFirstTransfer,
                IsLoyalty = freeTransferDetails.FreeLoyaltyTransfer,
                LoyaltyCount = freeTransferDetails.LoyaltyCount
            };

            return new ServiceResponse<FxRateResponseRakModel>(response);
        }

        public async Task<ServiceResponse<MoneyTransferFreeTransferEligiblity>> GetUserFreeTransferEigibilityLevel(Guid userId, CancellationToken cancellationToken = default(CancellationToken))
        {
            //check User exists or not
            if (userId == null)
            {
                _logger.LogWarning($"Due to invalid user id, Fx rates failed to get from RAK");
                return new ServiceResponse<MoneyTransferFreeTransferEligiblity>(false, BaseEnums.TransferStatusValidationMessage.UserNotExists.ToString());
            }

            var userExists = await _unitOfWork.Users.Any(x => x.Id == userId);

            if (!userExists)
            {
                _logger.LogWarning($"Due to invalid user id, Fx rates failed to get from RAK");
                return new ServiceResponse<MoneyTransferFreeTransferEligiblity>(false, BaseEnums.TransferStatusValidationMessage.UserNotExists.ToString());
            }

            var disableLoyaltyWaive = await _featureManager.IsEnabledAsync(FeatureFlags.Mt_DisableLoyaltyWaive);
            var freeTransferDetails = await
                this._unitOfWork.MoneyTransferTransactions.GetFreeTransferDetails(userId, _rakSettings.LoyaltyLimitAmount, Convert.ToDateTime(_rakSettings.LoyaltyImplementDate), _rakSettings.LoyaltyLimitCount, disableLoyaltyWaive, cancellationToken);

            var ResponseRateObject = new MoneyTransferFreeTransferEligiblity()
            {
                IsFirstTransfer = freeTransferDetails.FreeFirstTransfer,
                IsLoyalty = freeTransferDetails.FreeLoyaltyTransfer,
                LoyaltyCount = freeTransferDetails.LoyaltyCount
            };

            return new ServiceResponse<MoneyTransferFreeTransferEligiblity>(ResponseRateObject);
        }

        public async Task<ServiceResponse<bool>> GetTransferLimitEligibility(Guid userId, string transferMethod, string countryCode, decimal transactionAmount)
        {
            MoneyTransferType transferType;
            if (transferMethod.ToUpper() == TransferMethod.BANKTRANSFER.ToString())
            {
                transferType = MoneyTransferType.OutsideUAE;
            }
            else if (transferMethod.ToUpper() == TransferMethod.CASHPICKUP.ToString())
            {
                transferType = MoneyTransferType.RAKMoneyCashPayout;
            }
            else if (transferMethod.ToUpper() == TransferMethod.DIRECTTRANSFER.ToString())
            {
                transferType = MoneyTransferType.DirectTransfer;
            }
            else
            {
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.TransferMethodNotExists.ToString());
            }

            // Check if user exists or not
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == userId && !x.IsBlocked, u => u.CardHolder);
            if (user is null)
            {
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.UserNotExists.ToString());
            }

            if (transactionAmount <= 0)
            {
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.AmountNotExists.ToString());
            }

            if (transferType == MoneyTransferType.DirectTransfer)
            {
                var totalAmount = transactionAmount + this._moneyTransferServiceSettings.DirectTransferFee + this._moneyTransferServiceSettings.DirectTransferVAT;
                if (totalAmount > this._moneyTransferServiceSettings.DirectTransferMaxAmountToSend)
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.AmountTooHigh.ToString());
                }

                if (totalAmount < this._moneyTransferServiceSettings.DirectTransferMinAmountToSend)
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.AmountTooLow.ToString());
                }

                var tryGetBalance = await this.GetBalance(user);
                if (!tryGetBalance.IsSuccessful)
                {
                    return new ServiceResponse<bool>(false, tryGetBalance.ErrorMessage);
                }

                if (tryGetBalance.Data < totalAmount)
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.InsufficientBalance.ToString());
                }

                // Check user monthly limit.
                var monthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var monthEnd = monthStart.AddMonths(1);
                var moneyTransferTransactions = await _unitOfWork.MoneyTransferTransactions.FindAsync(
                    t => t.MoneyTransferBeneficiary.UserId == userId
                    && t.CreatedDate >= monthStart
                    && t.CreatedDate <= monthEnd
                    && (t.Status == Status.SUCCESSFUL || t.Status == Status.PENDING));

                var totalDirectMonthlyAmount = moneyTransferTransactions.Sum(transaction => transaction.SendAmount) + transactionAmount;

                if (totalDirectMonthlyAmount > this._moneyTransferServiceSettings.DirectTransferMaxAmountToSendPerMonth)
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.UserMonthlyAmountLimitReached.ToString());
                }
            }
            else
            {
                if (string.IsNullOrEmpty(countryCode))
                {
                    return new ServiceResponse<bool>(false, RechargeStatusValidationMessage.CountryNotExists.ToString());
                }

                //Check user monthly limit
                var now = DateTime.Now;
                var startDate = new DateTime(now.Year, now.Month, 1);
                var endDate = startDate.AddMonths(1);

                var moneyTransferTransactionsCount = await _unitOfWork.MoneyTransferTransactions.CountAsync(record => record.MoneyTransferBeneficiary.UserId == userId
                                                                                                && record.CreatedDate >= startDate
                                                                                                && record.CreatedDate <= endDate
                                                                                                && record.TransferType == TransactionType.RAKMoney
                                                                                                && (record.Status == BaseEnums.Status.SUCCESSFUL
                                                                                                   || record.Status == BaseEnums.Status.PENDING));

                var moneyTransferTransactionsAmount = await _unitOfWork.MoneyTransferTransactions.DecimalSumAsync(record => record.MoneyTransferBeneficiary.UserId == userId
                                                                                                && record.CreatedDate >= startDate
                                                                                                && record.CreatedDate <= endDate
                                                                                                && record.TransferType == TransactionType.RAKMoney
                                                                                                && (record.Status == BaseEnums.Status.SUCCESSFUL
                                                                                                   || record.Status == BaseEnums.Status.PENDING), a => a.SendAmount);

                var totalMonthlyCount = moneyTransferTransactionsCount + 1;
                if (totalMonthlyCount > _moneyTransferServiceSettings.UserMonthlyTransactionCountLimit)
                {
                    return new ServiceResponse<bool>(false, BaseEnums.TransferStatusValidationMessage.UserNoOfTransactionsLimitReached.ToString());
                }

                var totalMonthlyAmount = moneyTransferTransactionsAmount + transactionAmount;
                if (totalMonthlyAmount > _moneyTransferServiceSettings.UserMonthlyTransactionAmountLimit)
                {
                    return new ServiceResponse<bool>(false, BaseEnums.TransferStatusValidationMessage.UserMonthlyAmountLimitReached.ToString());
                }

                // TEMP FOR WU
                var nonWuCountries = new List<string> { "BD", "IN", "LK", "NP", "PH", "PK" };
                if (nonWuCountries.Contains(countryCode.ToUpper()) == false)
                {
                    // pick a random currency.
                    Random r = new Random();
                    int index = r.Next(0, nonWuCountries.Count - 1);
                    countryCode = nonWuCountries[index];
                }
                // END TEMP

                //Read other bank country code
                var country = await _unitOfWork.Countries.FirstOrDefaultAsync(z => z.Code == countryCode &&
                                                                                   ((transferType == BaseEnums.MoneyTransferType.RAKMoneyCashPayout && z.EligibleForCashPickUp) ||
                                                                                    (transferType == BaseEnums.MoneyTransferType.OutsideUAE && z.EligibleForBankTransfer)));
                if (country == null)
                {
                    return new ServiceResponse<bool>(false, BaseEnums.TransferStatusValidationMessage.CountryNotSupported.ToString());
                }

                if (transferMethod == MoneyTransferType.RAKMoneyCashPayout.ToString() && country.CashPickUpLatestRate > 0)
                {
                    decimal amountToReceive = (1 / country.CashPickUpLatestRate) * transactionAmount;
                    if ((country.Code == CountryEnable.PH.ToString() && amountToReceive > _maxPhReceiveAmount) ||
                        (country.Code == CountryEnable.NP.ToString() && amountToReceive > _maxNpReceiveAmount) ||
                        (country.Code == CountryEnable.PK.ToString() && amountToReceive > _maxPkReceiveAmount))
                    {
                        return new ServiceResponse<bool>(false, TransferStatusValidationMessage.ExceedMaxAmountLimit.ToString());
                    }
                }

                //checking RAK transaction limits
                var limitResultMessage = await this.CheckIfPartnerLimitIsReached(userId, transferType, countryCode, transactionAmount);
                if (!string.IsNullOrEmpty(limitResultMessage))
                {
                    _logger.LogWarning($"Due to {limitResultMessage}, money transfer limit eligibility failed for {userId}");
                    return new ServiceResponse<bool>(false, limitResultMessage);
                }

                _logger.LogWarning($"Checking daily spend limit");
                // Check if max daily limit is reached.
                var dailyLimit = await this._transactionsB2CService.GetDailySpendAmount(new GetDailySpendAmountRequest()
                {
                    CitizenId = user.CardHolderId,
                    Date = DateTime.Now,
                    SourceClient = "RAK"
                });

                if (dailyLimit.IsSuccessful)
                {
                    _logger.LogWarning($"Daily spend limit was successful. Amount: {dailyLimit.Data.TransactionsAmount}");
                    var totalAmount = dailyLimit.Data.TransactionsAmount + transactionAmount;
                    if (totalAmount > 10000)
                    {
                        _logger.LogWarning($"Daily spend limit is above 10,000. Daily Amount: {dailyLimit.Data.TransactionsAmount}. Total Amount: {totalAmount}");
                        return new ServiceResponse<bool>(false, "DailySpendLimitExceeded");
                    }
                    else
                    {
                        _logger.LogWarning($"Daily spend limit is below 10,000. Daily Amount: {dailyLimit.Data.TransactionsAmount}, Total Amount: {totalAmount}");
                    }
                }
                else
                {
                    _logger.LogWarning($"Daily spend limit was not successful. Error: {dailyLimit.ErrorMessage}");

                }
            }

            return new ServiceResponse<bool>(true);
        }

        public async Task<ServiceResponse<SendMoneyTransferResultDto>> SendMoneyTransfer(MoneyTransferTransaction moneyTransferTransaction)
        {
            moneyTransferTransaction.SendAmount = Math.Round(moneyTransferTransaction.SendAmount, 2);

            // Validate transfer.
            var isValid = await this.IsTransferValid(moneyTransferTransaction);
            if (isValid.IsSuccessful == false)
            {
                return new ServiceResponse<SendMoneyTransferResultDto>(false, isValid.ErrorMessage);
            }

            if (moneyTransferTransaction.TransferType == TransactionType.Direct)
            {
                return await SendDirectTransfer(moneyTransferTransaction);
            }
            else
            {
                return await SendInternationalTransfer(moneyTransferTransaction);
            }
        }


        public async Task<ServiceResponse<bool>> IsTransferValid(MoneyTransferTransaction moneyTransferTransaction)
        {
            if (moneyTransferTransaction.SendAmount == 0)
            {
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.SendAmountNotExists.ToString());
            }

            var beneficiary = (await _unitOfWork.MoneyTransferBeneficiaries.FindAsync(b => b.Id == moneyTransferTransaction.MoneyTransferBeneficiaryId
                                                                                           && !b.IsDeleted,
                                                                                      i => i.User, i => i.User.CardHolder)).FirstOrDefault();
            if (beneficiary is null)
            {
                _logger.LogWarning($"Money transfer failed for {moneyTransferTransaction.MoneyTransferBeneficiaryId} => Invalid beneficiary.");
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.BeneficiaryNotExists.ToString());
            }

            moneyTransferTransaction.UserId = beneficiary.UserId;

            var user = beneficiary.User;
            var cardNumber = user.CardHolder.CardNumber;
            var cardSerialNumber = user.CardHolder.CardSerialNumber;


            if (string.IsNullOrEmpty(cardNumber))
            {
                _logger.LogWarning($"Money transfer failed for {beneficiary.Id} and the name {beneficiary.FirstName} {beneficiary.LastName} => Invalid card number.");
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.InvalidCardNumber.ToString());
            }
            else if (string.IsNullOrEmpty(cardSerialNumber))
            {
                _logger.LogWarning($"Money transfer failed for {beneficiary.Id} and the name {beneficiary.FirstName} {beneficiary.LastName} => Invalid card serial number.");
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.InvalidCardSerialNumber.ToString());
            }

            if (moneyTransferTransaction.TransferType == TransactionType.Direct)
            {
                var totalAmount = moneyTransferTransaction.SendAmount + this._moneyTransferServiceSettings.DirectTransferFee + this._moneyTransferServiceSettings.DirectTransferVAT;

                if (totalAmount > this._moneyTransferServiceSettings.DirectTransferMaxAmountToSend)
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.AmountTooHigh.ToString());
                }

                if (totalAmount < this._moneyTransferServiceSettings.DirectTransferMinAmountToSend || moneyTransferTransaction.SendAmount <= 0)
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.AmountTooLow.ToString());
                }

                var tryGetBalance = await this.GetBalance(user);
                if (!tryGetBalance.IsSuccessful)
                {
                    return new ServiceResponse<bool>(false, tryGetBalance.ErrorMessage);
                }

                if (tryGetBalance.Data < totalAmount)
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.InsufficientBalance.ToString());
                }

                // Check user monthly limit.
                var monthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var nextMonthStart = monthStart.AddMonths(1);

                var moneyTransferTransactions = await _unitOfWork.MoneyTransferTransactions.FindAsync(
                    t => t.UserId == moneyTransferTransaction.UserId
                    && t.TransferType == TransactionType.Direct
                    && t.CreatedDate >= monthStart
                    && t.CreatedDate < nextMonthStart
                    && (t.Status == Status.SUCCESSFUL || t.Status == Status.PENDING));

                var totalMonthlyAmount = moneyTransferTransactions.Sum(t => t.SendAmount) + moneyTransferTransaction.SendAmount;

                if (totalMonthlyAmount > this._moneyTransferServiceSettings.DirectTransferMaxAmountToSendPerMonth)
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.UserMonthlyAmountLimitReached.ToString());
                }

                if (beneficiary.LinkedUserId != null)
                {
                    var receiver = await this._unitOfWork.Users.FirstOrDefaultAsync(
                        u => !u.IsBlocked
                        && !u.IsDeleted
                        && u.Id == beneficiary.LinkedUserId,
                        u => u.CardHolder);


                    if (receiver != null)
                    {
                        if (receiver.ApplicationId != MobileApplicationId.C3Pay)
                        {
                            _logger.LogWarning($"User is not a C3Pay user.");
                            return new ServiceResponse<bool>(false, TransferStatusValidationMessage.TransferError.ToString());
                        }

                        _logger.LogInformation($"Receiver found. Checking card status of serial number: {receiver.CardHolder.CardSerialNumber}");
                        var tryGetStatus = await this.GetBalance(receiver);
                        if (tryGetStatus.IsSuccessful)
                        {

                        }
                        else
                        {
                            _logger.LogError($"Check failed. Error: {tryGetStatus.ErrorMessage}");
                            return new ServiceResponse<bool>(false, tryGetStatus.ErrorMessage);
                        }

                    }
                }
                _logger.LogWarning($"Check for a blocked card done.");

            }
            else
            {
                var disableOnHold = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferDisableSettingOnHold);
                if (disableOnHold && (user.MoneyTransferProfileStatus != MoneyTransferProfileStatus.Created || beneficiary.Status != Status.APPROVED))
                {
                    _logger.LogWarning($"Money transfer failed for {moneyTransferTransaction.UserId} => Profile Pending.");
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.ProfilePending.ToString());
                }

                //check user is not EH user
                if (user.ApplicationId == MobileApplicationId.C3Pay && user.CardHolder.BelongsToExchangeHouse)
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.ExchangeHouseUser.ToString());
                }

                if (string.IsNullOrEmpty(moneyTransferTransaction.SendCurrency))
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.SendCurrencyNotExists.ToString());
                }

                // Check user monthly limit.
                var monthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var nextMonthStart = monthStart.AddMonths(1);
                var moneyTransferTransactions = await _unitOfWork.MoneyTransferTransactions.FindAsync(
                    t => t.MoneyTransferBeneficiary.UserId == moneyTransferTransaction.UserId
                    && t.CreatedDate >= monthStart
                    && t.CreatedDate < nextMonthStart
                    && t.TransferType == TransactionType.RAKMoney
                    && (t.Status == Status.SUCCESSFUL || t.Status == Status.PENDING));

                var totalMonthlyAmount = moneyTransferTransactions.Sum(transaction => transaction.SendAmount) + moneyTransferTransaction.SendAmount;
                var totalMonthlyCount = moneyTransferTransactions.Count() + 1;

                if (totalMonthlyCount > _moneyTransferServiceSettings.UserMonthlyTransactionCountLimit)
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.UserNoOfTransactionsLimitReached.ToString());
                }

                if (totalMonthlyAmount > _moneyTransferServiceSettings.UserMonthlyTransactionAmountLimit)
                {
                    return new ServiceResponse<bool>(false, TransferStatusValidationMessage.UserMonthlyAmountLimitReached.ToString());
                }

                // Checking RAK transaction limits.
                var getRakLimit = await this.CheckIfPartnerLimitIsReached(beneficiary.User.Id, beneficiary.TransferType, beneficiary.CountryCode, moneyTransferTransaction.SendAmount);
                if (!string.IsNullOrEmpty(getRakLimit))
                {
                    _logger.LogWarning($"Money transfer failed for {beneficiary.Id} and the name {beneficiary.FirstName} {beneficiary.LastName} => {getRakLimit}.");
                    return new ServiceResponse<bool>(false, getRakLimit);
                }


            }

            return new ServiceResponse<bool>(true);
        }

        public async Task<ServiceResponse<Status>> AddExternalTransaction(Guid transactionId)
        {
            //-------------------------------------------------------------------
            //---------- Read Money Transfer Transaction from DB ----------------
            //-------------------------------------------------------------------
            var moneyTransferTransaction = await _unitOfWork.MoneyTransferTransactions.FirstOrDefaultAsync(x => x.Id == transactionId,
                                                                                                           i => i.MoneyTransferBeneficiary,
                                                                                                           i => i.MoneyTransferBeneficiary.ExternalBeneficiary,
                                                                                                           i => i.MoneyTransferStatusSteps,
                                                                                                           i => i.User,
                                                                                                           i => i.User.CardHolder,
                                                                                                           i => i.ExternalTransaction,
                                                                                                           i => i.Transaction,
                                                                                                           i => i.MoneyTransferBeneficiary.Country);

            if (moneyTransferTransaction == null)
            {
                _logger.LogWarning($"Due to invalid transaction, money transfer failed for {moneyTransferTransaction.ReferenceNumber}");

                return new ServiceResponse<BaseEnums.Status>(BaseEnums.Status.FAILED);
            }

            //If status is onhold, then check if the conversionRate is lesser than the current conversionRate
            if (moneyTransferTransaction.Status == Status.ONHOLD)
            {
                decimal conversionRate = 0;
                //Get the Fx Rate and Transfer fees
                var fxRateData = await GetFxRates(moneyTransferTransaction.UserId, moneyTransferTransaction.ReceiveCurrency, moneyTransferTransaction.SendAmount, ((TransferMethod)moneyTransferTransaction.MoneyTransferBeneficiary.TransferType).ToString());
                if (fxRateData.IsSuccessful)
                {
                    conversionRate = Convert.ToDecimal(fxRateData.Data.FxRates.FirstOrDefault().FxConversionRates.FirstOrDefault()?.Rate);
                }

                if (!fxRateData.IsSuccessful || moneyTransferTransaction.ConversionRate > conversionRate)
                {
                    moneyTransferTransaction.Status = BaseEnums.Status.PENDINGREVERSE;
                    moneyTransferTransaction.Remarks = "Rate lesser from start of transfer";

                    // Add an entry to money transfer status progress.
                    moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                    moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                    {
                        Status = Status.PENDINGREVERSE,
                        Log = moneyTransferTransaction.Remarks,
                        Message = ConstantParam.UM_TransferFailedButWillBeReversed
                    });

                    await _unitOfWork.CommitAsync();

                    return new ServiceResponse<BaseEnums.Status>(BaseEnums.Status.COMPLETED);
                }
            }

            var disableOnHold = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferDisableSettingOnHold);
            if (disableOnHold && moneyTransferTransaction.MoneyTransferBeneficiary.ExternalBeneficiary == null)
            {
                _logger.LogWarning($"Due to invalid external beneficiary, money transfer failed for {moneyTransferTransaction.ReferenceNumber}");

                moneyTransferTransaction.Status = BaseEnums.Status.FAILED;
                moneyTransferTransaction.Remarks = BaseEnums.TransferStatusValidationMessage.BeneficiaryNotExists.ToString();
                moneyTransferTransaction.UpdatedDate = DateTime.Now;

                // Add an entry to money transfer status progress.
                moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                // Hide any failed steps.
                var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                foreach (var failedStep in failedSteps)
                {
                    failedStep.Hide = true;
                }

                moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                {
                    Status = Status.FAILED,
                    Log = "Transaction failed because the external beneficiary object was null.",
                    Message = ConstantParam.UM_TransferFailed
                });

                // Call failed event.
                await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                {
                    new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                    {
                        Amount = moneyTransferTransaction.SendAmount,
                        BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                        Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                        ErrorMessage = "Transaction failed because the external beneficiary object was null."
                    }, Status.FAILED)
                });

                await _unitOfWork.CommitAsync();

                return new ServiceResponse<BaseEnums.Status>(BaseEnums.Status.FAILED);
            }


            if (moneyTransferTransaction.MoneyTransferBeneficiary.ExternalBeneficiary == null)
            {
                moneyTransferTransaction.Status = Status.ONHOLD;
                moneyTransferTransaction.Remarks = string.Empty;
                moneyTransferTransaction.UpdatedDate = DateTime.Now;

                // Add an entry to money transfer status progress.
                moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                {
                    Status = Status.ONHOLD,
                    Log = "Transaction is on hold",
                    Hide = true
                });

                await _unitOfWork.CommitAsync();
            }
            else
            {
                moneyTransferTransaction.Status = Status.PENDING;
            }

            //If already completed, skip the process.
            if (moneyTransferTransaction != null && moneyTransferTransaction.ExternalTransaction != null)
            {
                return new ServiceResponse<BaseEnums.Status>(BaseEnums.Status.COMPLETED);
            }

            //Calculate the transaction amount
            var transactionDebitAmount = moneyTransferTransaction.SendAmount + moneyTransferTransaction.TotalCharges.GetValueOrDefault();

            if (moneyTransferTransaction.MoneyTransferBeneficiary.ExternalBeneficiary != null)
            {
                //Update Beneficiary details
                var updateBeneficiaryData = await UpdateBeneficiaryDetails(moneyTransferTransaction);
                if (!updateBeneficiaryData.IsSuccessful)
                {
                    await CancelSpinTheWheel(moneyTransferTransaction);

                    return updateBeneficiaryData;
                }
            }

            var user = moneyTransferTransaction.User;
            var cardNumber = user.CardHolder.CardNumber;
            var cardSerialNumber = user.CardHolder.CardSerialNumber;

            if (string.IsNullOrEmpty(cardNumber) || string.IsNullOrEmpty(cardSerialNumber))
            {
                _logger.LogWarning($"Due to invalid card number or serial number, money transfer failed for {moneyTransferTransaction.ReferenceNumber} and the name {moneyTransferTransaction.MoneyTransferBeneficiary.FirstName} {moneyTransferTransaction.MoneyTransferBeneficiary.LastName}");

                await CancelSpinTheWheel(moneyTransferTransaction);

                moneyTransferTransaction.Status = BaseEnums.Status.FAILED;
                moneyTransferTransaction.Remarks = BaseEnums.TransferStatusValidationMessage.InvalidCardNumber.ToString();
                moneyTransferTransaction.UpdatedDate = DateTime.Now;

                // Add an entry to money transfer status progress.
                moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                // Hide any failed steps.
                var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                foreach (var failedStep in failedSteps)
                {
                    failedStep.Hide = true;
                }

                moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                {
                    Status = Status.FAILED,
                    Log = $"Transaction failed because the card number or card serial number was not found.",
                    Message = ConstantParam.UM_TransferFailed
                });

                // Call failed event.
                await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                {
                    new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                    {
                        Amount = moneyTransferTransaction.SendAmount,
                        BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                        Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                        ErrorMessage = "Transaction failed because the card number or card serial number was not found."
                    }, Status.FAILED)
                });

                await _unitOfWork.CommitAsync();

                return new ServiceResponse<BaseEnums.Status>(BaseEnums.Status.FAILED);
            }

            //----------------------------------------------------------------------------------------------------
            //---------- do the redemption from PPS & update PPS transaction in DB--------------------------------
            //----------------------------------------------------------------------------------------------------        
            if (moneyTransferTransaction.Transaction == null)
            {
                //--------------------------------------------------------------------
                //------------------ Check Balance from PPS---------------------------
                //--------------------------------------------------------------------
                var balanceResponse = await GetBalance(user);
                var tryCount = moneyTransferTransaction.TriesCount.GetValueOrDefault() + 1;
                moneyTransferTransaction.TriesCount = tryCount;

                //If PPS API Call failed
                if (!balanceResponse.IsSuccessful)
                {
                    _logger.LogWarning($" Due to {balanceResponse.ErrorMessage}, transaction failed for {moneyTransferTransaction.ReferenceNumber} ");

                    if (tryCount > _rakSettings.MaxTransactionTriesCount)
                    {
                        await CancelSpinTheWheel(moneyTransferTransaction);

                        moneyTransferTransaction.Status = BaseEnums.Status.FAILED;

                        // Add an entry to money transfer status progress.
                        moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                        // Hide any failed steps.
                        var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                        foreach (var failedStep in failedSteps)
                        {
                            failedStep.Hide = true;
                        }

                        moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                        {
                            Status = Status.FAILED,
                            Log = $"Transaction failed because we were unable to get user's balance.",
                            ProviderRemarks = balanceResponse.ErrorMessage,
                            Message = ConstantParam.UM_TransferFailed
                        });

                        // Call failed event.
                        await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                        {
                            new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                            {
                                Amount = moneyTransferTransaction.SendAmount,
                                BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                                Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                                ErrorMessage = $"Transaction failed because we were unable to get user's balance. Error: {balanceResponse.ErrorMessage}"
                            }, Status.FAILED)
                        });
                    }
                    else
                    {
                        moneyTransferTransaction.Status = BaseEnums.Status.PENDING;
                    }

                    moneyTransferTransaction.Remarks = balanceResponse.ErrorMessage;
                    moneyTransferTransaction.UpdatedDate = DateTime.Now;

                    await _unitOfWork.CommitAsync();

                    return new ServiceResponse<BaseEnums.Status>(moneyTransferTransaction.Status);
                }

                //Check the current balance
                if (balanceResponse.Data < transactionDebitAmount)
                {
                    _logger.LogWarning($"Due to less balance of {transactionDebitAmount}, money transfer transaction failed for {moneyTransferTransaction.ReferenceNumber} ");

                    await CancelSpinTheWheel(moneyTransferTransaction);

                    moneyTransferTransaction.Status = BaseEnums.Status.FAILED;
                    moneyTransferTransaction.Remarks = BaseEnums.TransferStatusValidationMessage.InsufficientBalance.ToString();
                    moneyTransferTransaction.UpdatedDate = DateTime.Now;

                    // Add an entry to money transfer status progress.
                    moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                    // Hide any failed steps.
                    var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                    foreach (var failedStep in failedSteps)
                    {
                        failedStep.Hide = true;
                    }

                    moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                    {
                        Status = Status.FAILED,
                        Log = $"Transaction failed because of insufficient funds. Balance: {balanceResponse.Data}. Transactions Amount: {transactionDebitAmount}",
                        Message = ConstantParam.UM_TransferFailedInsufficientFunds
                    });

                    // Call failed event.
                    await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                    {
                        new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                        {
                            Amount = moneyTransferTransaction.SendAmount,
                            BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                            Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                            ErrorMessage = $"Transaction failed because of insufficient funds. Balance: {balanceResponse.Data}. Transactions Amount: {transactionDebitAmount}"
                        }, Status.FAILED)
                    });

                    await _unitOfWork.CommitAsync();

                    return new ServiceResponse<BaseEnums.Status>(BaseEnums.Status.FAILED);
                }

                var ppsDebitAmount = transactionDebitAmount * 100;
                var debitUser = await DebitInternationalTransfer(moneyTransferTransaction, ppsDebitAmount, null);
                if (!debitUser.IsSuccessful)
                {
                    await CancelSpinTheWheel(moneyTransferTransaction);

                    return new ServiceResponse<BaseEnums.Status>(moneyTransferTransaction.Status);
                }
            }

            //----------------------------------------------------------------------------------------------------
            //--------------------------------- Send Money Transfer to RAK ---------------------------------------
            //----------------------------------------------------------------------------------------------------
            if (moneyTransferTransaction.Status == Status.PENDING && moneyTransferTransaction.ExternalTransaction == null)
            {
                // Find transfer method.
                var countryCode = moneyTransferTransaction.MoneyTransferBeneficiary.CountryCode;
                MoneyTransferMethod transferMethod;

                if (moneyTransferTransaction.MoneyTransferBeneficiary.TransferType == MoneyTransferType.OutsideUAE)
                {
                    transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.MoneyTransferProvider.CountryCode == countryCode
                                                                                                              && x.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer
                                                                                                              && x.IsActive == true,
                                                                                                              include => include.MoneyTransferProvider);
                }
                else if (moneyTransferTransaction.MoneyTransferBeneficiary.TransferType == MoneyTransferType.RAKMoneyCashPayout)
                {
                    transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.MoneyTransferProvider.CountryCode == countryCode
                                                                                          && x.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.CashPickup
                                                                                          && x.IsActive == true,
                                                                                          include => include.MoneyTransferProvider);
                }
                else if (moneyTransferTransaction.MoneyTransferBeneficiary.TransferType == MoneyTransferType.Wallet)
                {
                    transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.MoneyTransferProvider.CountryCode == countryCode
                                                                                          && x.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.Wallet
                                                                                          && x.IsActive == true,
                                                                                          include => include.MoneyTransferProvider);
                }
                else
                {
                    transferMethod = null;
                }

                if (transferMethod != null && transferMethod.MoneyTransferProvider.WUSendMoneyEnabled)
                {
                    // New code.
                    var placeOfBirth = await GetPlaceOfBirthAsync(user.CardHolder, moneyTransferTransaction.MoneyTransferBeneficiary.CountryCode);
                    var wuTransferRequest = new CreateTransferRequest()
                    {
                        TransactionType = moneyTransferTransaction.TransferType.ToString(),
                        PurposeCode = moneyTransferTransaction.MoneyTransferReasonId.ToString(),
                        Sender = new MoneyTransferSender()
                        {
                            CustomerIdType = moneyTransferTransaction.MoneyTransferBeneficiary.DocumentType,
                            CustomerIdNumber = moneyTransferTransaction.MoneyTransferBeneficiary.DocumentNumber,
                            PlaceOfBirth = placeOfBirth,
                            Nationality = await GetTwoLetterNationalityCodeAsync(user.CardHolder.Nationality, moneyTransferTransaction.MoneyTransferBeneficiary.CountryCode),
                            DateOfBirth = await GetDateOfBirthAsync(user.CardHolder, moneyTransferTransaction.MoneyTransferBeneficiary.CountryCode)
                        },
                        Receiver = new MoneyTransferReceiver()
                        {
                            BeneficiaryId = moneyTransferTransaction.MoneyTransferBeneficiary.ExternalBeneficiary.ExternalId
                        },
                        Amount = new MoneyTransferAmount()
                        {
                            Currency = moneyTransferTransaction.SendCurrency,
                            Amount = moneyTransferTransaction.SendAmount.ToString(),
                        },
                        ApiVersion = "2.4.0",
                        C3ReferenceNumber = moneyTransferTransaction.ReferenceNumber
                    };
                    _logger.LogInformation($"{moneyTransferTransaction.ReferenceNumber} Request: {JsonConvert.SerializeObject(wuTransferRequest)}");
                    moneyTransferTransaction.TransactionPlaceOfBirth = string.IsNullOrWhiteSpace(placeOfBirth) ? string.Empty : placeOfBirth;
                    ServiceResponse<Transfer> wuTransferResult = null;

                    try
                    {
                        wuTransferResult = await this._externalProviderMoneyTransferService.SendMoneyTransfer(wuTransferRequest);
                    }
                    catch (Exception exception)
                    {
                        await CancelSpinTheWheel(moneyTransferTransaction);

                        if (reversalsExceptionMessages.Any(exception.Message.StartsWith))
                        {
                            _logger.LogWarning($"Due to {exception.Message}, Transaction to RAK is marked for reversal for {moneyTransferTransaction.ReferenceNumber} and the beneficiary name :{moneyTransferTransaction.MoneyTransferBeneficiary.FirstName} {moneyTransferTransaction.MoneyTransferBeneficiary.LastName} ");
                            moneyTransferTransaction.Status = BaseEnums.Status.PENDINGREVERSE;

                            // Add an entry to money transfer status progress.
                            moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                            // Hide any failed steps.
                            var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                            foreach (var failedStep in failedSteps)
                            {
                                failedStep.Hide = true;
                            }

                            moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                            {
                                Status = Status.PENDINGREVERSE,
                                Log = $"Transaction failed because of a RAK SendMoney API exception.",
                                ProviderRemarks = exception.ToString(),
                                Message = ConstantParam.UM_TransferFailedButWillBeReversed
                            });
                        }
                        else
                        {
                            moneyTransferTransaction.Status = BaseEnums.Status.FAILED;

                            // Add an entry to money transfer status progress.
                            moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                            // Hide any failed steps.
                            var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                            foreach (var failedStep in failedSteps)
                            {
                                failedStep.Hide = true;
                            }

                            moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                            {
                                Status = Status.FAILED,
                                Log = $"Transaction failed because of a RAK SendMoney API exception.",
                                ProviderRemarks = exception.ToString(),
                                Message = ConstantParam.UM_TransferFailed
                            });

                            // Call failed event.
                            await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                            {
                                new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                                {
                                    Amount = moneyTransferTransaction.SendAmount,
                                    BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                                    Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                                    ErrorMessage = $"Transaction failed because of a RAK SendMoney API exception. Error: {exception.ToString()}"
                                }, Status.FAILED)
                            });
                        }

                        moneyTransferTransaction.Remarks = string.Join(" : ", "Exception", exception.Message);
                        moneyTransferTransaction.UpdatedDate = DateTime.Now;

                        await _unitOfWork.CommitAsync();

                        return new ServiceResponse<BaseEnums.Status>(false, TransferStatusValidationMessage.RAKConnectionIssue.ToString());
                    }

                    if (wuTransferResult.IsSuccessful == false || (wuTransferResult.Data != null && wuTransferResult.Data.Status != ExternalStatus.P.ToString()))
                    {
                        await CancelSpinTheWheel(moneyTransferTransaction);

                        // Check if error is a auto-reversal status.
                        if (reversalsErrorMessages.Any(wuTransferResult.ErrorMessage.StartsWith))
                        {
                            _logger.LogWarning($"WU:: Due to {wuTransferResult.ErrorMessage}, Transaction to RAK is marked for reversal for {moneyTransferTransaction.ReferenceNumber} and the beneficiary name :{moneyTransferTransaction.MoneyTransferBeneficiary.FirstName} {moneyTransferTransaction.MoneyTransferBeneficiary.LastName} ");

                            moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                            moneyTransferTransaction.Remarks = wuTransferResult.ErrorMessage;
                            moneyTransferTransaction.UpdatedDate = DateTime.Now;

                            // Add an entry to money transfer status progress.
                            moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                            // Hide any failed steps.
                            var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                            foreach (var failedStep in failedSteps)
                            {
                                failedStep.Hide = true;
                            }

                            moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                            {
                                Status = Status.PENDINGREVERSE,
                                Log = $"Transaction failed because of a RAK SendMoney API error.",
                                ProviderRemarks = wuTransferResult.ErrorMessage,
                                Message = ConstantParam.UM_TransferFailedButWillBeReversed
                            });

                            //Call failed event.
                            await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                            {
                                new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                                {
                                    Amount = moneyTransferTransaction.SendAmount,
                                    BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                                    Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                                    ErrorMessage = $"Transaction failed because of a RAK SendMoney API error. Error: {wuTransferResult.ErrorMessage}"
                                }, Status.FAILED)
                            });

                            await _unitOfWork.CommitAsync();

                            return new ServiceResponse<BaseEnums.Status>(moneyTransferTransaction.Status);
                        }
                        else
                        {
                            _logger.LogWarning($"Due to {wuTransferResult.ErrorMessage}, Transaction to RAK failed for {moneyTransferTransaction.ReferenceNumber} and the beneficiary name :{moneyTransferTransaction.MoneyTransferBeneficiary.FirstName} {moneyTransferTransaction.MoneyTransferBeneficiary.LastName} ");

                            moneyTransferTransaction.Status = BaseEnums.Status.FAILED;
                            moneyTransferTransaction.Remarks = wuTransferResult.ErrorMessage;
                            moneyTransferTransaction.UpdatedDate = DateTime.Now;

                            // Add an entry to money transfer status progress.
                            moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                            // Hide any failed steps.
                            var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                            foreach (var failedStep in failedSteps)
                            {
                                failedStep.Hide = true;
                            }

                            moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                            {
                                Status = Status.FAILED,
                                Log = $"Transaction failed because of a RAK SendMoney API error.",
                                ProviderRemarks = wuTransferResult.ErrorMessage,
                                Message = ConstantParam.UM_TransferFailed
                            });

                            await _unitOfWork.CommitAsync();

                            await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                            {
                                new Tuple<string, MoneyTransferEvent, Status>(user.CardHolderId, new MoneyTransferEvent()
                                {
                                    Amount = moneyTransferTransaction.SendAmount,
                                    BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                                    Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                                    ErrorMessage = wuTransferResult.ErrorMessage
                                }, moneyTransferTransaction.Status)
                            });

                            return new ServiceResponse<Status>(moneyTransferTransaction.Status);
                        }
                    }

                    //Create the external transaction in DB

                    moneyTransferTransaction.CashPickupPin = wuTransferResult.Data.CashPickUpPin;
                    moneyTransferTransaction.CashPickUpPoint = wuTransferResult.Data.CashPickUpPoint;

                    moneyTransferTransaction.ExternalTransaction = new MoneyTransferExternalTransaction
                    {
                        ExternalStatus = wuTransferResult.Data.Status,
                        StatusDescription = wuTransferResult.Data.StatusDescription,
                        ExternalTransactionId = wuTransferResult.Data.ProviderTransferId,
                        StartDate = Convert.ToDateTime(wuTransferResult.Data.StartDate),
                        EndDate = Convert.ToDateTime(wuTransferResult.Data.EndDate),
                        Type = wuTransferResult.Data.TransactionType,
                        ChargesAmount = string.IsNullOrEmpty(wuTransferResult.Data.Charges.Amount) ? 0 : TypeUtility.GetDecimalFromString(wuTransferResult.Data.Charges.Amount),
                        ChargesCurrency = wuTransferResult.Data.Charges.Currency,
                        WaivedCharge = string.IsNullOrEmpty(wuTransferResult.Data.Charges.Discount) ? 0 : TypeUtility.GetDecimalFromString(wuTransferResult.Data.Charges.Discount),
                        TotalCharges = string.IsNullOrEmpty(wuTransferResult.Data.Charges.TotalAmount) ? 0 : TypeUtility.GetDecimalFromString(wuTransferResult.Data.Charges.TotalAmount),
                        TotalCreditAmount = string.IsNullOrEmpty(wuTransferResult.Data.TotalCreditAmount) ? 0 : TypeUtility.GetDecimalFromString(wuTransferResult.Data.TotalCreditAmount),
                        TotalDebitAmount = string.IsNullOrEmpty(wuTransferResult.Data.TotalDebitAmount) ? 0 : TypeUtility.GetDecimalFromString(wuTransferResult.Data.TotalDebitAmount),
                        CreditCurrency = string.IsNullOrEmpty(wuTransferResult.Data.CreditCurrency) ? ConstantParam.DefaultCurrency : wuTransferResult.Data.CreditCurrency,
                        DebitCurrency = string.IsNullOrEmpty(wuTransferResult.Data.DebitCurrency) ? ConstantParam.DefaultCurrency : wuTransferResult.Data.DebitCurrency
                    };


                    if (string.IsNullOrEmpty(wuTransferResult.Data.MsgId) == false)
                    {
                        moneyTransferTransaction.ExternalTransaction.MessageId = new Guid(wuTransferResult.Data.MsgId);
                    }

                    moneyTransferTransaction.Transaction.StatusCode = "00";
                    _logger.LogInformation($"External transaction updated successfully in DB for the money transfer transaction : {moneyTransferTransaction.ReferenceNumber} ");
                }
                else
                {
                    // Old code.
                    // Build rak Request
                    var placeOfBirth = await GetPlaceOfBirthAsync(moneyTransferTransaction.User.CardHolder, moneyTransferTransaction.MoneyTransferBeneficiary.CountryCode);
                    var sendTransferRequest = new MoneyTransferRequestRakModel()
                    {
                        TransactionType = moneyTransferTransaction.TransferType.ToString(),
                        PurposeCode = moneyTransferTransaction.MoneyTransferReasonId.ToString(),
                        From = new MoneyTransferFrom()
                        {
                            CustomerIdType = moneyTransferTransaction.MoneyTransferBeneficiary.DocumentType,
                            CustomerIdNumber = moneyTransferTransaction.MoneyTransferBeneficiary.DocumentNumber,
                            PlaceOfBirth = placeOfBirth,
                            Nationality = await GetTwoLetterNationalityCodeAsync(moneyTransferTransaction.User.CardHolder.Nationality, moneyTransferTransaction.MoneyTransferBeneficiary.CountryCode),
                            DateOfBirth = await GetDateOfBirthAsync(moneyTransferTransaction.User.CardHolder, moneyTransferTransaction.MoneyTransferBeneficiary.CountryCode)
                        },
                        To = new MoneyTransferTo()
                        {
                            RakBeneficiaryId = moneyTransferTransaction.MoneyTransferBeneficiary.ExternalBeneficiary.ExternalId
                        },
                        Value = new MoneyTransferValue()
                        {
                            Currency = moneyTransferTransaction.SendCurrency,
                            Amount = moneyTransferTransaction.SendAmount.ToString(),
                        }
                    };
                    _logger.LogInformation($"{moneyTransferTransaction.ReferenceNumber} Request: {JsonConvert.SerializeObject(sendTransferRequest)}");
                    moneyTransferTransaction.TransactionPlaceOfBirth = string.IsNullOrWhiteSpace(placeOfBirth) ? string.Empty : placeOfBirth;
                    //Call Rak API
                    ServiceResponse<RakResponse<MoneyTransferResponseRakModel>> sendTransferResult = null;

                    try
                    {
                        sendTransferResult = await this._rakService.SendMoneyTransfer(moneyTransferTransaction.ReferenceNumber, sendTransferRequest);
                    }
                    catch (Exception exception)
                    {
                        await CancelSpinTheWheel(moneyTransferTransaction);

                        if (reversalsExceptionMessages.Any(exception.Message.StartsWith))
                        {
                            _logger.LogWarning($"Due to {exception.Message}, Transaction to RAK is marked for reversal for {moneyTransferTransaction.ReferenceNumber} and the beneficiary name :{moneyTransferTransaction.MoneyTransferBeneficiary.FirstName} {moneyTransferTransaction.MoneyTransferBeneficiary.LastName} ");
                            moneyTransferTransaction.Status = BaseEnums.Status.PENDINGREVERSE;

                            // Add an entry to money transfer status progress.
                            moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                            // Hide any failed steps.
                            var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                            foreach (var failedStep in failedSteps)
                            {
                                failedStep.Hide = true;
                            }

                            moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                            {
                                Status = Status.PENDINGREVERSE,
                                Log = $"Transaction failed because of a RAK SendMoney API exception.",
                                ProviderRemarks = exception.ToString(),
                                Message = ConstantParam.UM_TransferFailedButWillBeReversed
                            });
                        }
                        else
                        {
                            moneyTransferTransaction.Status = BaseEnums.Status.FAILED;

                            // Add an entry to money transfer status progress.
                            moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                            // Hide any failed steps.
                            var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                            foreach (var failedStep in failedSteps)
                            {
                                failedStep.Hide = true;
                            }

                            moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                            {
                                Status = Status.FAILED,
                                Log = $"Transaction failed because of a RAK SendMoney API exception.",
                                ProviderRemarks = exception.ToString(),
                                Message = ConstantParam.UM_TransferFailed
                            });

                            // Call failed event.
                            await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                            {
                                new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                                {
                                    Amount = moneyTransferTransaction.SendAmount,
                                    BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                                    Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                                    ErrorMessage = $"Transaction failed because of a RAK SendMoney API exception. Error: {exception.ToString()}"
                                }, Status.FAILED)
                            });
                        }

                        moneyTransferTransaction.Remarks = string.Join(" : ", "Exception", exception.Message);
                        moneyTransferTransaction.UpdatedDate = DateTime.Now;

                        await _unitOfWork.CommitAsync();

                        return new ServiceResponse<BaseEnums.Status>(false, BaseEnums.TransferStatusValidationMessage.RAKConnectionIssue.ToString());
                    }

                    if (!sendTransferResult.IsSuccessful || (sendTransferResult.Data != null && sendTransferResult.Data.Data.Status != BaseEnums.ExternalStatus.P.ToString()))
                    {
                        await CancelSpinTheWheel(moneyTransferTransaction);

                        // Check if error is a auto-reversal status.
                        if (reversalsErrorMessages.Any(sendTransferResult.ErrorMessage.StartsWith))
                        {
                            _logger.LogWarning($"Due to {sendTransferResult.ErrorMessage}, Transaction to RAK is marked for reversal for {moneyTransferTransaction.ReferenceNumber} and the beneficiary name :{moneyTransferTransaction.MoneyTransferBeneficiary.FirstName} {moneyTransferTransaction.MoneyTransferBeneficiary.LastName} ");

                            moneyTransferTransaction.Status = BaseEnums.Status.PENDINGREVERSE;
                            moneyTransferTransaction.Remarks = sendTransferResult.ErrorMessage;
                            moneyTransferTransaction.UpdatedDate = DateTime.Now;

                            // Add an entry to money transfer status progress.
                            moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                            // Hide any failed steps.
                            var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                            foreach (var failedStep in failedSteps)
                            {
                                failedStep.Hide = true;
                            }

                            moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                            {
                                Status = Status.PENDINGREVERSE,
                                Log = $"Transaction failed because of a RAK SendMoney API error.",
                                ProviderRemarks = sendTransferResult.ErrorMessage,
                                Message = ConstantParam.UM_TransferFailedButWillBeReversed
                            });

                            // Call failed event.
                            await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                            {
                                new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                                {
                                    Amount = moneyTransferTransaction.SendAmount,
                                    BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                                    Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                                    ErrorMessage = $"Transaction failed because of a RAK SendMoney API error. Error: {sendTransferResult.ErrorMessage}"
                                }, Status.FAILED)
                            });

                            await _unitOfWork.CommitAsync();

                            return new ServiceResponse<BaseEnums.Status>(moneyTransferTransaction.Status);
                        }
                        else
                        {
                            _logger.LogWarning($"Due to {sendTransferResult.ErrorMessage}, Transaction to RAK failed for {moneyTransferTransaction.ReferenceNumber} and the beneficiary name :{moneyTransferTransaction.MoneyTransferBeneficiary.FirstName} {moneyTransferTransaction.MoneyTransferBeneficiary.LastName} ");

                            moneyTransferTransaction.Status = BaseEnums.Status.FAILED;
                            moneyTransferTransaction.Remarks = sendTransferResult.ErrorMessage;
                            moneyTransferTransaction.UpdatedDate = DateTime.Now;

                            // Add an entry to money transfer status progress.
                            moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                            // Hide any failed steps.
                            var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                            foreach (var failedStep in failedSteps)
                            {
                                failedStep.Hide = true;
                            }

                            moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                            {
                                Status = Status.FAILED,
                                Log = $"Transaction failed because of a RAK SendMoney API error.",
                                ProviderRemarks = sendTransferResult.ErrorMessage,
                                Message = ConstantParam.UM_TransferFailed
                            });

                            await _unitOfWork.CommitAsync();

                            await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                        {
                            new Tuple<string, MoneyTransferEvent, Status>(user.CardHolderId, new MoneyTransferEvent()
                            {
                                Amount = moneyTransferTransaction.SendAmount,
                                BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                                Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                                ErrorMessage = sendTransferResult.ErrorMessage
                            }, moneyTransferTransaction.Status)
                        });

                            return new ServiceResponse<BaseEnums.Status>(moneyTransferTransaction.Status);
                        }
                    }

                    //Create the external transaction in DB
                    string response = System.Text.Json.JsonSerializer.Serialize(sendTransferResult.Data.Data);


                    moneyTransferTransaction.CashPickupPin = sendTransferResult.Data.Data.CashPickUpPin;
                    moneyTransferTransaction.CashPickUpPoint = sendTransferResult.Data.Data.CashPickUpPoint;

                    moneyTransferTransaction.ExternalTransaction = new MoneyTransferExternalTransaction
                    {
                        ExternalStatus = sendTransferResult.Data.Data.Status,
                        StatusDescription = sendTransferResult.Data.Data.StatusDesc,
                        MessageId = sendTransferResult.Data.MessageId,
                        ExternalTransactionId = sendTransferResult.Data.Data.PaymentRefId,
                        StartDate = Convert.ToDateTime(sendTransferResult.Data.Data.StartDate),
                        EndDate = Convert.ToDateTime(sendTransferResult.Data.Data.EndDate),
                        Type = sendTransferResult.Data.Data.TransactionType,
                        ChargesAmount = string.IsNullOrEmpty(sendTransferResult.Data.Data.Charges.Amount) ? 0 : TypeUtility.GetDecimalFromString(sendTransferResult.Data.Data.Charges.Amount),
                        ChargesCurrency = sendTransferResult.Data.Data.Charges.Currency,
                        WaivedCharge = string.IsNullOrEmpty(sendTransferResult.Data.Data.Charges.Discount) ? 0 : TypeUtility.GetDecimalFromString(sendTransferResult.Data.Data.Charges.Discount),
                        TotalCharges = string.IsNullOrEmpty(sendTransferResult.Data.Data.Charges.TotalAmount) ? 0 : TypeUtility.GetDecimalFromString(sendTransferResult.Data.Data.Charges.TotalAmount),
                        TotalCreditAmount = string.IsNullOrEmpty(sendTransferResult.Data.Data.TotalCreditAmount) ? 0 : TypeUtility.GetDecimalFromString(sendTransferResult.Data.Data.TotalCreditAmount),
                        TotalDebitAmount = string.IsNullOrEmpty(sendTransferResult.Data.Data.TotalDebitAmount) ? 0 : TypeUtility.GetDecimalFromString(sendTransferResult.Data.Data.TotalDebitAmount),
                        CreditCurrency = string.IsNullOrEmpty(sendTransferResult.Data.Data.CreditCurrency) ? ConstantParam.DefaultCurrency : sendTransferResult.Data.Data.CreditCurrency,
                        DebitCurrency = string.IsNullOrEmpty(sendTransferResult.Data.Data.DebitCurrency) ? ConstantParam.DefaultCurrency : sendTransferResult.Data.Data.DebitCurrency
                    };

                    moneyTransferTransaction.Transaction.StatusCode = "00";

                }
            }

            await _unitOfWork.CommitAsync();


            return new ServiceResponse<BaseEnums.Status>(BaseEnums.Status.COMPLETED);
        }

        #region Rewards
        private async Task CreateSpinTheWheel(MoneyTransferTransaction moneyTransferTransaction, User user)
        {
            var enableRewardSpinTheWheel = await _featureManager.IsEnabledAsync(FeatureFlags.EnableRewardSpinTheWheel);
            if (enableRewardSpinTheWheel && (moneyTransferTransaction.Status == Status.PENDING || moneyTransferTransaction.Status == Status.SUCCESSFUL))
            {
                var enableRewardSpinTheWheelCardholderExp = await _featureManager.IsEnabledAsync(FeatureFlags.EnableSpinTheWheelCardHolderExperiment);
                //Check the user is part of the experiment
                if (enableRewardSpinTheWheelCardholderExp && !IsUserAllowedForSpinTheWheel(user.CardHolderId, _rewardServiceSettings, user.PhoneNumber))
                {
                    return;
                }

                var rewardAssigned = await _rewardService.CreateSpin(new RewardCreateSpinDto
                {
                    Username = user.PhoneNumber,
                    SegmentCode = UserSegment.MT_GE.ToString(),
                    ActionCode = RewardAction.MT_SP_RT.ToString(),
                    ReferenceNumber = moneyTransferTransaction.Id.ToString(),
                    ActionAssociatedCountryCode = moneyTransferTransaction.MoneyTransferBeneficiary?.CountryCode
                });

                if (rewardAssigned.IsSuccessful)
                {
                    moneyTransferTransaction.IsRewardAssigned = true;
                }
                else
                {
                    moneyTransferTransaction.IsRewardAssignmentFailed = true;
                }

                await _unitOfWork.CommitAsync();
            }
        }

        private async Task CancelSpinTheWheel(MoneyTransferTransaction moneyTransferTransaction)
        {
            var enableRewardSpinTheWheel = await _featureManager.IsEnabledAsync(FeatureFlags.EnableRewardSpinTheWheel);
            if (enableRewardSpinTheWheel)
            {
                if (!string.IsNullOrEmpty(_rewardServiceSettings.TestAccountUsernames) && !_rewardServiceSettings.TestAccountUsernames.Split('|').Contains(moneyTransferTransaction.User?.PhoneNumber))
                {
                    return;
                }

                await _rewardService.CancelSpin(new RewardCancelSpinDto
                {
                    Username = moneyTransferTransaction.User?.PhoneNumber,
                    ReferenceNumber = moneyTransferTransaction.Id.ToString()
                });
            }
        }

        public async Task<ServiceResponse> ResendMoneyTransferUnassignedRewards()
        {
            var resendTransactions = await _unitOfWork.MoneyTransferTransactions.FindAsync(f => f.IsRewardAssignmentFailed == true && f.IsRewardAssigned == false &&
                                                                                                f.RewardAssignmentRetryCount <= _rewardServiceSettings.RetryCount &&
                                                                                                f.TransferType == TransactionType.RAKMoney,
                                                                                           null,
                                                                                           false,
                                                                                           0,
                                                                                           50,
                                                                                           false,
                                                                                           i => i.User, i => i.User.UserSegmentation, i => i.MoneyTransferBeneficiary);


            var enableRewardSpinTheWheelCardholderExp = await _featureManager.IsEnabledAsync(FeatureFlags.EnableSpinTheWheelCardHolderExperiment);

            foreach (var item in resendTransactions)
            {
                //Assign the retry count
                item.RewardAssignmentRetryCount++;

                //If the status of the transaction is not pending or successful, we will not create the spin
                if (!(item.Status == Status.PENDING || item.Status == Status.SUCCESSFUL))
                {
                    item.IsRewardAssignmentFailed = false;
                    continue;
                }


                //Check the user is part of the experiment
                if (enableRewardSpinTheWheelCardholderExp && !IsUserAllowedForSpinTheWheel(item.User.CardHolderId, _rewardServiceSettings, item.User.PhoneNumber))
                {
                    item.IsRewardAssignmentFailed = false;
                    continue;
                }

                var rewardAssigned = await _rewardService.CreateSpin(new RewardCreateSpinDto
                {
                    Username = item.User.PhoneNumber,
                    SegmentCode = UserSegment.MT_GE.ToString(),
                    ActionCode = RewardAction.MT_SP_RT.ToString(),
                    ReferenceNumber = item.Id.ToString(),
                    ActionAssociatedCountryCode = item.MoneyTransferBeneficiary.CountryCode
                });

                if (rewardAssigned.IsSuccessful)
                {
                    item.IsRewardAssigned = true;
                }
                else
                {
                    item.IsRewardAssignmentFailed = true;
                }
            }

            await _unitOfWork.CommitAsync();

            return new ServiceResponse();
        }

        #endregion Rewards

        /// <summary>
        /// Update Beneficiary Details
        /// </summary>
        /// <param name="moneyTransferTransaction"></param>
        /// <returns></returns>
        private async Task<ServiceResponse<Status>> UpdateBeneficiaryDetails(MoneyTransferTransaction moneyTransferTransaction)
        {
            var beneficiary = moneyTransferTransaction.MoneyTransferBeneficiary;
            if (beneficiary.TransferType == MoneyTransferType.OutsideUAE && !beneficiary.IsBranchSelected.Value && beneficiary.CountryCode != CountryEnable.PH.ToString())
            {

                var lastSuccessfulTransaction = await this._unitOfWork.MoneyTransferTransactions.GetBankLastSuccessfulTransaction(beneficiary.BankName, beneficiary.CountryCode, TransactionType.RAKMoney);
                if (lastSuccessfulTransaction == null)
                {
                    this._logger.LogWarning(ConstantParam.SkippedUpdatingBeneficiaryNoTransferFound.ToString());
                }

                var lastSuccessfulTransactionBeneficiary = lastSuccessfulTransaction.MoneyTransferBeneficiary;

                var isSameIdentifierCode1 = beneficiary.IdentifierCode1 == lastSuccessfulTransactionBeneficiary.IdentifierCode1;
                var isSameIdentifierCode2 = beneficiary.IdentifierCode2 == lastSuccessfulTransactionBeneficiary.IdentifierCode2;
                if (isSameIdentifierCode1 && isSameIdentifierCode2)
                {
                    this._logger.LogWarning(ConstantParam.SkippedUpdatingBeneficiarySameBranch.ToString());
                }
                else
                {
                    beneficiary.BankName = lastSuccessfulTransactionBeneficiary.BankName;
                    beneficiary.BankBranchName = lastSuccessfulTransactionBeneficiary.BankBranchName;
                    beneficiary.IdentifierCode1 = lastSuccessfulTransactionBeneficiary.IdentifierCode1;
                    beneficiary.IdentifierCode2 = lastSuccessfulTransactionBeneficiary.IdentifierCode2;
                    beneficiary.Address1 = lastSuccessfulTransactionBeneficiary.Address1;
                    beneficiary.Address2 = lastSuccessfulTransactionBeneficiary.Address2;
                    beneficiary.MarkAsUpdated();


                    var externalBeneficiaryResult = await this._rakService.GetMoneyTransferBeneficiaryById(beneficiary.DocumentNumber, beneficiary.ExternalBeneficiary.ExternalId);
                    if (!externalBeneficiaryResult.IsSuccessful)
                    {
                        this._logger.LogWarning(ConstantParam.FetchingExternalBeneficiaryFailed, externalBeneficiaryResult.ErrorMessage);

                        moneyTransferTransaction.Status = BaseEnums.Status.FAILED;
                        moneyTransferTransaction.Remarks = BaseEnums.TransferStatusValidationMessage.FetchingExternalBeneficiaryFailed.ToString();
                        moneyTransferTransaction.UpdatedDate = DateTime.Now;

                        // Add an entry to money transfer status progress.
                        moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                        // Hide any failed steps.
                        var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                        foreach (var failedStep in failedSteps)
                        {
                            failedStep.Hide = true;
                        }

                        moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                        {
                            Status = Status.FAILED,
                            Log = $"Transaction failed because we were unable to get beneficiary details.",
                            ProviderRemarks = externalBeneficiaryResult.ErrorMessage,
                            Message = ConstantParam.UM_TransferFailed
                        });

                        // Call failed event.
                        await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                        {
                            new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                            {
                                Amount = moneyTransferTransaction.SendAmount,
                                BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                                Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                                ErrorMessage = "Transaction failed because we were unable to get beneficiary details."
                            }, Status.FAILED)
                        });

                        await _unitOfWork.CommitAsync();

                        return new ServiceResponse<Status>(false, BaseEnums.TransferStatusValidationMessage.FetchingExternalBeneficiaryFailed.ToString());
                    }

                    var externalBeneficiary = externalBeneficiaryResult.Data;

                    var updateResult = await _rakService.UpdateRAKMoneyTransferBeneficiary(beneficiary.DocumentNumber, beneficiary.ExternalBeneficiary.ExternalId, new NewBeneficiaryRequestRakModel()
                    {
                        AccountNumber = beneficiary.AccountNumber,
                        Address = new AddressRakModel()
                        {
                            Address1 = beneficiary.Address1,
                            Address2 = beneficiary.Address2,
                            Country = externalBeneficiary.Address.Country
                        },
                        FirstName = beneficiary.FirstName,
                        LastName = beneficiary.LastName,
                        RakBeneficiayId = externalBeneficiary.RakBeneficiayId,
                        BankCountry = externalBeneficiary.BankCountry,
                        BankName = beneficiary.BankName,
                        BankBranchName = beneficiary.BankBranchName,
                        Identifier1Name = externalBeneficiary.Identifier1Name,
                        IdentifierCode1 = beneficiary.IdentifierCode1,
                        Identifier2Name = externalBeneficiary.Identifier2Name,
                        IdentifierCode2 = beneficiary.IdentifierCode2,
                        TransferType = externalBeneficiary.TransferType,
                        Documents = new List<DocumentRakModel>()
                        {
                            new DocumentRakModel() { Number = beneficiary.DocumentNumber, Type = beneficiary.DocumentType }
                        }
                    });

                    if (!updateResult.IsSuccessful)
                    {
                        this._logger.LogWarning(ConstantParam.UpdatingBeneficiaryFailed, updateResult.ErrorMessage);

                        moneyTransferTransaction.Status = BaseEnums.Status.FAILED;
                        moneyTransferTransaction.Remarks = BaseEnums.TransferStatusValidationMessage.BeneficiaryUpdateFailed.ToString();
                        moneyTransferTransaction.UpdatedDate = DateTime.Now;

                        // Add an entry to money transfer status progress.
                        moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                        // Hide any failed steps.
                        var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                        foreach (var failedStep in failedSteps)
                        {
                            failedStep.Hide = true;
                        }

                        moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                        {
                            Status = Status.FAILED,
                            Log = $"Transaction failed because we were unable to update beneficiary details.",
                            ProviderRemarks = updateResult.ErrorMessage,
                            Message = ConstantParam.UM_TransferFailed
                        });

                        // Call failed event.
                        await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                        {
                            new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                            {
                                Amount = moneyTransferTransaction.SendAmount,
                                BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                                Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                                ErrorMessage = "Transaction failed because we were unable to update beneficiary details."
                            }, Status.FAILED)
                        });

                        await _unitOfWork.CommitAsync();

                        return new ServiceResponse<Status>(false, BaseEnums.TransferStatusValidationMessage.BeneficiaryUpdateFailed.ToString());
                    }

                    await this._unitOfWork.CommitAsync();

                }
            }

            return new ServiceResponse<Status>(Status.COMPLETED);
        }

        /// <summary>
        /// Debiting a user for International Money Transfer
        /// </summary>
        /// <param name="moneyTransferTransaction"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        private async Task<ServiceResponse<Status>> DebitInternationalTransfer(MoneyTransferTransaction moneyTransferTransaction, decimal amount, string refNumber)
        {
            var user = moneyTransferTransaction.User;
            var cardNumber = user.CardHolder.CardNumber;
            var cardSerialNumber = user.CardHolder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);
            var narration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.MoneyTransfer), moneyTransferTransaction.MoneyTransferBeneficiary.FirstName);

            #region Creating Reference number

            string referenceNumber = string.Empty;
            if (refNumber == null)
            {
                var referencePrefix = BaseEnums.TransactionPrefix.RMT.ToString();
                var referenceDigits = 12;
                referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
                while (await this._unitOfWork.Transactions.Any(t => t.ReferenceNumber == referenceNumber))
                {
                    referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
                }
            }
            else
            {
                referenceNumber = refNumber;
            }


            #endregion Creating Reference Number

            ServiceResponse<PPSWebAuthResponseModel> redemptionResponse = null;
            try
            {
                redemptionResponse = await _ppsWebAuthService.DoRedemption(new RedemptionRequest()
                {
                    Amount = decimal.Truncate(amount),
                    CardSerialNumber = cardSerialNumber,
                    CardPanNumber = cardPanNumber,
                    Narration = narration,
                    MerchantLoopCode = "5000",
                    TerminalId = TransactionMerchantCodeService.GetMerchantCode(user.CardHolder.BelongsToExchangeHouse, user.ApplicationId, TransactionMerchantCodeFeature.MoneyTransfer),
                    ReferenceNumber = referenceNumber,
                    Description = narration
                });
            }
            catch (Exception ex)
            {
                this._logger.LogError(ex, "Error debiting user due to PPS exception for reference number {referenceNumber}", referenceNumber);

                moneyTransferTransaction.Status = BaseEnums.Status.FAILED;
                moneyTransferTransaction.Remarks = $"{TransferStatusValidationMessage.PPSConnectionIssue} :: {TransferStatusValidationMessage.ExceptionOccurred}";
                moneyTransferTransaction.UpdatedDate = DateTime.Now;

                // Add an entry to money transfer status progress.
                moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                // Hide any failed steps.
                var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                foreach (var failedStep in failedSteps)
                {
                    failedStep.Hide = true;
                }

                moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                {
                    Status = Status.FAILED,
                    Log = $"Transaction failed because we could not debit the user.",
                    ProviderRemarks = ex.ToString(),
                    Message = ConstantParam.UM_TransferFailed
                });

                // Call failed event.
                await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                {
                    new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                    {
                        Amount = moneyTransferTransaction.SendAmount,
                        BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                        Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                        ErrorMessage = $"Transaction failed because we could not debit the user. Error: {ex}"
                    }, Status.FAILED)
                });

                await _unitOfWork.CommitAsync();

                return new ServiceResponse<BaseEnums.Status>(false, moneyTransferTransaction.Remarks);
            }

            // PPS connection technical issue.
            if (!redemptionResponse.IsSuccessful)
            {
                _logger.LogWarning($"Due to pps service connection, Redemption failed for {referenceNumber} and the redemption amount :{amount} ");

                if (moneyTransferTransaction.TriesCount > _rakSettings.MaxTransactionTriesCount)
                {
                    moneyTransferTransaction.Status = BaseEnums.Status.FAILED;

                    // Add an entry to money transfer status progress.
                    moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                    // Hide any failed steps.
                    var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                    foreach (var failedStep in failedSteps)
                    {
                        failedStep.Hide = true;
                    }

                    moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                    {
                        Status = Status.FAILED,
                        Log = $"Transaction failed because we could not debit the user.",
                        ProviderRemarks = redemptionResponse.ErrorMessage,
                        Message = ConstantParam.UM_TransferFailed
                    });

                    // Call failed event.
                    await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                    {
                        new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                        {
                            Amount = moneyTransferTransaction.SendAmount,
                            BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                            Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                            ErrorMessage = $"Transaction failed because we could not debit the user. Error: {redemptionResponse.ErrorMessage}"
                        }, Status.FAILED)
                    });
                }
                else
                {
                    moneyTransferTransaction.Status = BaseEnums.Status.PENDING;
                }

                moneyTransferTransaction.Remarks = BaseEnums.TransferStatusValidationMessage.PPSConnectionIssue.ToString();
                moneyTransferTransaction.UpdatedDate = DateTime.Now;

                await _unitOfWork.CommitAsync();

                return new ServiceResponse<BaseEnums.Status>(false, moneyTransferTransaction.Remarks);
            }

            // PPS response failed.
            if (redemptionResponse.Data.StatusCode != "00")
            {
                _logger.LogWarning($"Due to {redemptionResponse.Data.Message}, Redemption failed for {referenceNumber} and the redemption amount :{amount} ");

                moneyTransferTransaction.Status = BaseEnums.Status.FAILED;
                moneyTransferTransaction.Remarks = redemptionResponse.Data.Message.ToString();
                moneyTransferTransaction.UpdatedDate = DateTime.Now;

                // Add an entry to money transfer status progress.
                moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                // Hide any failed steps.
                var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                foreach (var failedStep in failedSteps)
                {
                    failedStep.Hide = true;
                }

                moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                {
                    Status = Status.FAILED,
                    Log = $"Transaction failed because we could not debit the user.",
                    ProviderRemarks = redemptionResponse.ErrorMessage,
                    Message = ConstantParam.UM_TransferFailed
                });

                // Call failed event.
                await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                {
                    new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                    {
                        Amount = moneyTransferTransaction.SendAmount,
                        BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                        Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                        ErrorMessage = $"Transaction failed because we could not debit the user. Error: {redemptionResponse.ErrorMessage}"
                    }, Status.FAILED)
                });

                await _unitOfWork.CommitAsync();

                return new ServiceResponse<BaseEnums.Status>(false, moneyTransferTransaction.Remarks);
            }

            // Create/update PPS Transaction.
            moneyTransferTransaction.Transaction = new Transaction
            {
                BillPayType = EnumUtility.GetDescriptionFromEnumValue(BaseEnums.PPSBillPayType.RMT),
                CreatedDate = DateTime.Now,
                UserId = moneyTransferTransaction.MoneyTransferBeneficiary.UserId,
                IsDeleted = false,
                PayeeId = redemptionResponse.Data.PayeeId,
                ServiceProvider = BaseEnums.CardPaymentServiceProvider.PPS,
                CardNumber = redemptionResponse.Data.Account.No,
                CardSerialNumber = redemptionResponse.Data.Account.Serial,
                CardAccountTerminalAddress = redemptionResponse.Data.TerminalAddress,
                CardAccountTerminalId = redemptionResponse.Data.TerminalId,
                Amount = TypeUtility.GetDecimalFromString(redemptionResponse.Data.Value.Amt) / 100,
                StatusDescription = redemptionResponse.Data.Message,
                Date = string.IsNullOrEmpty(redemptionResponse.Data.TimeStamp.Val) ? string.Empty : redemptionResponse.Data.TimeStamp.Val.ToString().Split(' ').First(),
                Time = string.IsNullOrEmpty(redemptionResponse.Data.TimeStamp.Val) ? string.Empty : redemptionResponse.Data.TimeStamp.Val.ToString().Split(' ').Last(),
                MacValue = redemptionResponse.Data.HashValue,
                Origin = redemptionResponse.Data.Origin,
                AuthenticationCode = redemptionResponse.Data.AuthCode,
                ReferenceNumber = redemptionResponse.Data.ReferenceId,
                EndBalance = TypeUtility.GetDecimalFromString(redemptionResponse.Data.EndBalanace.Amt),
                CardAccountId = BaseEnums.PPSAccountIdPrefix.RMT.GetHashCode().ToString() + BaseEnums.PPSMerchantType.RMT.GetHashCode().ToString() + EnumUtility.GetDescriptionFromEnumValue(BaseEnums.PPSBillPayType.RMT),
                StatusCode = BaseEnums.Status.PENDING.ToString()
            };

            await this._unitOfWork.CommitAsync();


            return new ServiceResponse<Status>(Status.COMPLETED);
        }

        public async Task<ServiceResponse<IEnumerable<MoneyTransferDetails>>> GetUserTransactions(Guid userId, MoneyTransferType? transferType, int? pageSize, int? pageNumber, string languageCode, CancellationToken cancellationToken = default(CancellationToken))
        {
            int? skipValue;
            int? pageRecords;

            if (pageSize > 0 && pageNumber > 0)
            {
                skipValue = pageNumber == 1 ? 0 : (pageNumber - 1) * pageSize;
                pageRecords = pageSize;
            }
            else
            {
                skipValue = null;
                pageRecords = null;
            }

            var checkMinSuspiciousDate = _moneyTransferServiceSettings.CheckMinSuspiciousDate;


            var user = await _unitOfWork.Users.GetUserById(userId, cancellationToken);
            var transactions = await _unitOfWork.MoneyTransferTransactions.GetUserTransactionsDetails(userId, transferType, skipValue, pageRecords, checkMinSuspiciousDate, cancellationToken);
            var thresholdValue = !string.IsNullOrWhiteSpace(_configuration["MoneyTransfer_RecentTransactionThresholdDays"]) ? Convert.ToInt32(_configuration["MoneyTransfer_RecentTransactionThresholdDays"]) : 3;

            foreach (var transaction in transactions)
            {
                // Set status to started for new transactions.
                if (transaction.Status == Status.PENDING && string.IsNullOrEmpty(transaction.Remarks))
                {
                    transaction.Status = Status.STARTED;
                }
                else if (transaction.Status == Status.ONHOLD)
                {
                    transaction.Status = Status.PENDING;
                }

                var csQueriesChangeEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferCsQueriesUpdate);
                if (csQueriesChangeEnabled)
                    transaction.SetTransactionStatusMessageAndVideo(languageCode, thresholdValue, user.CardHolder.Nationality);
                transaction.TransferMethod = ((TransferMethod)transaction.TransferType).ToString();
            }

            return new ServiceResponse<IEnumerable<MoneyTransferDetails>>(transactions);
        }

        public async Task<ServiceResponse<MoneyTransferTransaction>> GetTransactionReceipt(Guid transactionId, string languageCode = default)
        {
            languageCode = string.IsNullOrEmpty(languageCode) ? "en" : languageCode;
            var transaction = await _unitOfWork.MoneyTransferTransactions.FirstOrDefaultAsync(x => x.Id == transactionId,
                                                                                        x => x.MoneyTransferBeneficiary,
                                                                                        x => x.MoneyTransferBeneficiary.MoneyTransferReason,
                                                                                        x => x.MoneyTransferBeneficiary.Country,
                                                                                        x => x.ExternalTransaction,
                                                                                        x => x.User.CardHolder,
                                                                                        x => x.User,
                                                                                        x => x.MoneyTransferBeneficiary.LinkedUser,
                                                                                        x => x.MoneyTransferStatusSteps.Where(a => !a.Hide),
                                                                                        x => x.MoneyTransferBeneficiary.MoneyTransferSuspiciousInformation,
                                                                                        x => x.MoneyTransferBeneficiary.BeneficiaryAdditionalFields);

            var csQueriesChangeEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferCsQueriesUpdate);
            if (csQueriesChangeEnabled)
                transaction.SetTransactionStatusMessage(languageCode);

            // If there are no status steps, add default ones.
            if (transaction.MoneyTransferStatusSteps is null || transaction.MoneyTransferStatusSteps.Count == 0)
            {

                transaction.MoneyTransferStatusSteps = new List<MoneyTransferStatusStep>();

                // Add "Started" first.
                transaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                {
                    Status = Status.STARTED,
                    Timestamp = transaction.CreatedDate
                });

                // If the transfer was successful, add: Started => Successful.
                if (transaction.Status == Status.SUCCESSFUL)
                {
                    transaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                    {
                        Message = ConstantParam.UM_SuccessfulTransfer,
                        Status = Status.SUCCESSFUL,
                        Timestamp = transaction.CreatedDate
                    });
                }

                // If the transfer had failed or needs manual reversal, add: Started => Failed.
                else if (transaction.Status == Status.FAILED || transaction.Status == Status.NEEDSMANUALREVERSAL)
                {
                    transaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                    {
                        Message = ConstantParam.UM_TransferFailed,
                        Status = Status.FAILED,
                        Timestamp = transaction.CreatedDate
                    });
                }

                // If the transfer is pending reverse, add Started => Pending Reverse.
                else if (transaction.Status == Status.PENDINGREVERSE)
                {
                    transaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                    {
                        Message = ConstantParam.UM_TransferPendingReverse,
                        Status = Status.PENDINGFEEREVERSE,
                        Timestamp = transaction.CreatedDate
                    });
                }

                // If the transfer is reversed, add Started => Reverse.
                else if (transaction.Status == Status.REVERSED)
                {
                    transaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                    {
                        Message = ConstantParam.UM_TransferReversed_R,
                        Status = Status.REVERSED,
                        Timestamp = transaction.CreatedDate
                    });
                }

                // If the transfer is reversed, add Started => Reverse.
                else if (transaction.Status == Status.PENDING)
                {
                    transaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                    {
                        Message = ConstantParam.UM_TransferPending,
                        Status = Status.PENDING,
                        Timestamp = transaction.CreatedDate
                    });
                }
            }

            // Set status to started for new transactions.
            if (transaction.Status == Status.PENDING && string.IsNullOrEmpty(transaction.Remarks))
            {
                transaction.Status = Status.STARTED;
            }

            // If we have a Pending step or a PendingReversed step, we need to assume a future step.
            var lastItem = transaction.MoneyTransferStatusSteps.LastOrDefault();

            if (lastItem != null)
            {
                lastItem.IsCurrent = true;

                if (lastItem.Status == Status.PENDING)
                {
                    transaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                    {
                        Status = Status.SUCCESSFUL,
                        IsFuture = true
                    });
                }

                else if (lastItem.Status == Status.PENDINGREVERSE)
                {
                    transaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                    {
                        Status = Status.REVERSED,
                        IsFuture = true
                    });
                }

                else if (lastItem.Status == Status.STARTED)
                {
                    transaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                    {
                        Status = Status.SUCCESSFUL,
                        IsFuture = true
                    });
                }
                // If the last item is REVERSED, we need to show the correct message to the user.
                else if (lastItem.Status == Status.REVERSED)
                {
                    lastItem.Message = transaction.Remarks switch
                    {
                        ConstantParam.PE_InvalidBeneficiaryAccountNumberError => ConstantParam.UM_InvalidBeneficiaryAccountNumber_R,
                        ConstantParam.PE_AccountFrozenError => ConstantParam.UM_AccountFrozen_R,
                        ConstantParam.PE_IncorrectPurposeCodeError => ConstantParam.UM_IncorrectPurposeCode_R,
                        ConstantParam.PE_InvalidIfsCodeError => ConstantParam.UM_InvalidIfsCode_R,
                        ConstantParam.PE_BeneficiaryAccountClosedError => ConstantParam.UM_BeneficiaryAccountClosed_R,
                        ConstantParam.PE_InvalidBeneficiaryNameError => ConstantParam.UM_InvalidBeneficiaryName_R,
                        ConstantParam.PE_BeneficiaryBankDidNotRespondError => ConstantParam.UM_BeneficiaryBankDidNotRespond_R,
                        ConstantParam.PE_StopPaymentDoneError => ConstantParam.UM_StopPaymentDone_R,
                        _ => ConstantParam.UM_TransferReversed_R,
                    };
                }
            }

            if (transaction.MoneyTransferBeneficiary.TransferType == MoneyTransferType.Wallet)
            {
                if (transaction.MoneyTransferBeneficiary.BeneficiaryAdditionalFields != null && transaction.MoneyTransferBeneficiary.BeneficiaryAdditionalFields.Count > 0)
                {
                    var providerField = transaction.MoneyTransferBeneficiary.BeneficiaryAdditionalFields.FirstOrDefault(x => x.FieldCode == "SERVICE_PROVIDER");
                    if (providerField != null)
                    {
                        var provider = await this._unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.MoneyTransferMethodType == MoneyTransferMethodType.Wallet && x.ProviderCode == providerField.FieldValue);
                        if (provider != null)
                        {
                            transaction.MoneyTransferBeneficiary.WalletProvider = provider.Name;
                        }
                    }

                    var phoneNumber = transaction.MoneyTransferBeneficiary.BeneficiaryAdditionalFields.FirstOrDefault(x => x.FieldCode == "MOBILE_NO");
                    var countryCode = transaction.MoneyTransferBeneficiary.BeneficiaryAdditionalFields.FirstOrDefault(x => x.FieldCode == "COUNTRY_CODE");
                    transaction.MoneyTransferBeneficiary.PhoneNumber = $"+{countryCode?.FieldValue}{phoneNumber?.FieldValue}";
                }
            }

            return new ServiceResponse<MoneyTransferTransaction>(transaction);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="TransactionId"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<MoneyTransferTransaction>> GetTransactionReceiptReadOnly(Guid TransactionId)
        {
            var mttransaction = await _unitOfWorkReadOnly.MoneyTransferTransactions.FirstOrDefaultAsync(x => x.Id == TransactionId,
                                                                                        x => x.MoneyTransferBeneficiary,
                                                                                        x => x.MoneyTransferBeneficiary.MoneyTransferReason,
                                                                                        x => x.MoneyTransferBeneficiary.Country,
                                                                                        x => x.ExternalTransaction,
                                                                                        x => x.User.CardHolder,
                                                                                        x => x.User);
            return new ServiceResponse<MoneyTransferTransaction>(mttransaction);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="TransactionId"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<MoneyTransferTransaction>> UpdateTransactionFromExternal(Guid transactionId, bool calledFromJob = false)
        {
            await _unitOfWork.MoneyTransferTransactions.UpdateStatusCheckRetryCount(transactionId);

            //Read Money Transfer transaction from DB
            var moneyTransferTransaction = await _unitOfWork.MoneyTransferTransactions.FirstOrDefaultAsync(x => x.Id == transactionId,
                                                                                                           x => x.MoneyTransferBeneficiary,
                                                                                                           x => x.ExternalTransaction,
                                                                                                           x => x.Transaction,
                                                                                                           x => x.User,
                                                                                                           x => x.MoneyTransferBeneficiary.Country,
                                                                                                           x => x.User.CardHolder,
                                                                                                           x => x.MoneyTransferStatusSteps);

            if (moneyTransferTransaction == null)
            {
                _logger.LogWarning($"Due to invalid transaction, status updation failed for {transactionId}");
                return new ServiceResponse<MoneyTransferTransaction>(false, BaseEnums.TransferStatusValidationMessage.InvalidTransaction.ToString());
            }

            _logger.LogWarning($"Update Transaction From External :: Transaction is: {moneyTransferTransaction.ReferenceNumber}");

            var user = moneyTransferTransaction.User;
            var emiratesId = user.CardHolder.EmiratesId;

            //if the transaction is not send to rak.
            if (moneyTransferTransaction.ExternalTransaction == null)
            {
                return new ServiceResponse<MoneyTransferTransaction>(moneyTransferTransaction);
            }

            if (moneyTransferTransaction.Status == BaseEnums.Status.REVERSED
                || moneyTransferTransaction.Status == BaseEnums.Status.PENDINGREVERSE
                || moneyTransferTransaction.Status == BaseEnums.Status.FAILEDTOREVERSE
                || moneyTransferTransaction.Status == BaseEnums.Status.NEEDSMANUALREVERSAL)
            {
                return new ServiceResponse<MoneyTransferTransaction>(moneyTransferTransaction);
            }


            var countryCode = moneyTransferTransaction.MoneyTransferBeneficiary.CountryCode;
            MoneyTransferMethod transferMethod = null;

            if (moneyTransferTransaction.MoneyTransferBeneficiary.TransferType == MoneyTransferType.OutsideUAE)
            {
                transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.MoneyTransferProvider.CountryCode == countryCode
                                                                                                          && x.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer,
                                                                                                          include => include.MoneyTransferProvider);
            }
            else if (moneyTransferTransaction.MoneyTransferBeneficiary.TransferType == MoneyTransferType.RAKMoneyCashPayout)
            {
                transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.MoneyTransferProvider.CountryCode == countryCode
                                                                                      && x.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.CashPickup,
                                                                                      include => include.MoneyTransferProvider);
            }
            else if (moneyTransferTransaction.MoneyTransferBeneficiary.TransferType == MoneyTransferType.Wallet)
            {
                transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.MoneyTransferProvider.CountryCode == countryCode
                                                                                      && x.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.Wallet,
                                                                                      include => include.MoneyTransferProvider);
            }


            ServiceResponse<MoneyTransferResponseRakModel> transactionStatusResult = null;

            if (transferMethod != null && transferMethod.MoneyTransferProvider.WUSendMoneyEnabled)
            {
                var wuTransferResult = await this._externalProviderMoneyTransferService.GetMoneyTransferById(new GetTransferRequest()
                {
                    EmiratesId = emiratesId,
                    TransactionId = moneyTransferTransaction.ReferenceNumber.ToString(),
                    TransactionType = TransactionType.RAKMoney.ToString()
                });

                if (wuTransferResult.IsSuccessful == false)
                {
                    return new ServiceResponse<MoneyTransferTransaction>(false, BaseEnums.TransferStatusValidationMessage.RAKConnectionIssue.ToString());
                }

                transactionStatusResult = new ServiceResponse<MoneyTransferResponseRakModel>();
                transactionStatusResult.Data = new MoneyTransferResponseRakModel()
                {
                    Charges = new MoneyTransferCharges()
                    {
                        Amount = wuTransferResult.Data.Charges.Amount,
                        Currency = wuTransferResult.Data.Charges.Currency,
                        Discount = wuTransferResult.Data.Charges.Discount,
                        Tax = wuTransferResult.Data.Charges.Tax,
                        TotalAmount = wuTransferResult.Data.Charges.TotalAmount,
                    },
                    CreditCurrency = wuTransferResult.Data.CreditCurrency,
                    DebitCurrency = wuTransferResult.Data.DebitCurrency,
                    EndDate = wuTransferResult.Data.EndDate,
                    From = new MoneyTransferFrom()
                    {
                        CustomerIdNumber = wuTransferResult.Data.From.CustomerIdNumber,
                        CustomerIdType = wuTransferResult.Data.From.CustomerIdType
                    },
                    To = new MoneyTransferTo()
                    {
                        RakBeneficiaryId = wuTransferResult.Data.To.RakBeneficiaryId
                    },
                    Value = new MoneyTransferValue()
                    {
                        Amount = wuTransferResult.Data.Value.Amount,
                        Currency = wuTransferResult.Data.Value.Currency
                    },
                    Status = wuTransferResult.Data.Status,
                    StartDate = wuTransferResult.Data.StartDate,
                    TotalCreditAmount = wuTransferResult.Data.TotalCreditAmount,
                    TotalDebitAmount = wuTransferResult.Data.TotalDebitAmount,
                    TransactionId = wuTransferResult.Data.ProviderTransferId,
                    PaymentRefId = wuTransferResult.Data.C3ReferenceNumber,
                    PurposeCode = wuTransferResult.Data.PurposeCode,
                    StatusDesc = wuTransferResult.Data.StatusDescription,
                    TransactionType = wuTransferResult.Data.StatusDescription
                };
            }
            else
            {
                transactionStatusResult = await this._rakService.GetMoneyTransferTransactionStatus(moneyTransferTransaction.ReferenceNumber.ToString(), emiratesId, BaseEnums.TransactionType.RAKMoney.ToString());
                if (!transactionStatusResult.IsSuccessful)
                {
                    return new ServiceResponse<MoneyTransferTransaction>(false, BaseEnums.TransferStatusValidationMessage.RAKConnectionIssue.ToString());
                }
            }

            Status recordStatus = Status.PENDING;

            switch (transactionStatusResult.Data.Status)
            {
                case nameof(ExternalStatus.S):
                    recordStatus = Status.SUCCESSFUL;
                    break;

                case nameof(ExternalStatus.F):
                    recordStatus = Status.FAILED;
                    break;

                case nameof(ExternalStatus.REV):
                case nameof(ExternalStatus.REJ):
                    if (moneyTransferTransaction.CreatedDate < _moneyTransferServiceSettings.ReversalStartDate)
                    {
                        _logger.LogWarning($"UpdatePendingTransactions :: Created date is less than reversal start date, reference number {moneyTransferTransaction.ReferenceNumber}.");
                        recordStatus = Status.FAILED;
                    }
                    else if (allowRemarksForReversal.Any(transactionStatusResult.Data.StatusDesc.StartsWith))
                    {
                        recordStatus = Status.PENDINGREVERSE;
                    }
                    else
                    {
                        switch (_moneyTransferServiceSettings.ReversalMode)
                        {
                            case ReversalMode.None:
                                Log($"UpdatePendingTransactions :: Reversal is off, reference number {moneyTransferTransaction.ReferenceNumber}.");
                                recordStatus = Status.FAILED;
                                break;
                            case ReversalMode.EdenredCorporateIds:
                                // Check if this transaction was done by us.
                                if (moneyTransferTransaction.User.CardHolder.CorporateId == "99999" || moneyTransferTransaction.User.CardHolder.CorporateId == "99998")
                                {
                                    Log($"UpdatePendingTransactions :: Transaction belongs to edenred, marking it as PendingReversal, reference number {moneyTransferTransaction.ReferenceNumber}.");
                                    recordStatus = Status.PENDINGREVERSE;
                                }
                                else
                                {
                                    Log($"UpdatePendingTransactions :: Reversal is on only for edenred, reference number {moneyTransferTransaction.ReferenceNumber}.");
                                    recordStatus = Status.FAILED;
                                }
                                break;
                            case ReversalMode.All:
                                Log($"UpdatePendingTransactions :: Reversal is on, marking transaction as PendingReverse {moneyTransferTransaction.ReferenceNumber}.");
                                recordStatus = Status.PENDINGREVERSE;
                                break;
                            default:
                                break;
                        }
                    }
                    break;
                case nameof(ExternalStatus.P):
                    recordStatus = Status.PENDING;
                    break;

                default:
                    _logger.LogWarning($"UpdatePendingTransactions :: Unable to parse status for transaction {moneyTransferTransaction.ReferenceNumber}. Status: {transactionStatusResult.Data.Status}");
                    break;
            }

            await this.UpdateTransactionStatus(moneyTransferTransaction, moneyTransferTransaction.ReferenceNumber, recordStatus, transactionStatusResult.Data.StatusDesc, DateTime.Now, transactionStatusResult.Data.Status);


            return new ServiceResponse<MoneyTransferTransaction>(moneyTransferTransaction);
        }

        public async Task<ServiceResponse<MoneyTransferTransaction>> ManualStatusUpdate(string referenceNumber, Status status)
        {
            //Read Money Transfer transaction from DB
            var transaction = await _unitOfWork.MoneyTransferTransactions.FirstOrDefaultAsync(x => x.ReferenceNumber == referenceNumber,
                                                                                                             x => x.MoneyTransferBeneficiary,
                                                                                                             x => x.ExternalTransaction,
                                                                                                             x => x.Transaction,
                                                                                                             x => x.User,
                                                                                                             x => x.MoneyTransferBeneficiary.Country,
                                                                                                             x => x.User.CardHolder);

            if (transaction == null)
            {
                _logger.LogWarning($"Due to invalid transaction, status updation failed for {referenceNumber}");

                return new ServiceResponse<MoneyTransferTransaction>(false, BaseEnums.TransferStatusValidationMessage.InvalidTransaction.ToString());
            }

            var user = transaction.User;

            var emiratesId = user.CardHolder.EmiratesId;

            await this.UpdateTransactionStatus(transaction, transaction.ReferenceNumber, status, "", DateTime.Now, "");

            _logger.LogWarning($"Status updated successfully for {transaction.ReferenceNumber} and the name {transaction.MoneyTransferBeneficiary.FirstName} {transaction.MoneyTransferBeneficiary.LastName}");

            transaction.Status = status;

            return new ServiceResponse<MoneyTransferTransaction>(transaction);
        }

        public async Task<ServiceResponse<MoneyTransferTransaction>> UpdateTransactionStatus(MoneyTransferTransaction moneyTransferTransaction, string referenceNumber, Status recordStatus, string description, DateTime statusDate, string externalStatus, Guid? portalUserId = null, string portalEmailId = null)
        {
            //Read transactions
            moneyTransferTransaction ??= await _unitOfWork.MoneyTransferTransactions.FirstOrDefaultAsync(x => x.ReferenceNumber == referenceNumber,
                                                                                                         x => x.MoneyTransferBeneficiary,
                                                                                                         x => x.User,
                                                                                                         x => x.User.CardHolder,
                                                                                                         x => x.ExternalTransaction,
                                                                                                         x => x.MoneyTransferBeneficiary.Country,
                                                                                                         x => x.MoneyTransferStatusSteps);
            if (moneyTransferTransaction == null)
            {
                _logger.LogWarning($"Due to invalid transaction, status updation failed for {referenceNumber}");
                return new ServiceResponse<MoneyTransferTransaction>(false, BaseEnums.TransferStatusValidationMessage.InvalidTransaction.ToString());
            }

            if (moneyTransferTransaction.Status == BaseEnums.Status.REVERSED
                || moneyTransferTransaction.Status == BaseEnums.Status.PENDINGREVERSE
                || moneyTransferTransaction.Status == BaseEnums.Status.FAILEDTOREVERSE
                || moneyTransferTransaction.Status == BaseEnums.Status.NEEDSMANUALREVERSAL)
            {
                return new ServiceResponse<MoneyTransferTransaction>(false, "The update cannot proceed as the transaction has already been reversed.");
            }

            var initialStatus = moneyTransferTransaction.Status;
            var initialRemarks = moneyTransferTransaction.Remarks;
            var lastUpdateDate = moneyTransferTransaction.UpdatedDate ?? moneyTransferTransaction.CreatedDate;

            if (moneyTransferTransaction.ExternalTransaction == null)
            {
                _logger.LogWarning($"Due to invalid external transaction, status updation failed for {referenceNumber}");
            }

            if (!nonUpdatableStatuses.Contains(initialStatus))
            {
                moneyTransferTransaction.Status = recordStatus;
            }

            //update the transaction
            moneyTransferTransaction.Remarks = description;
            moneyTransferTransaction.UpdatedDate = DateTime.Now;

            if (moneyTransferTransaction.ExternalTransaction != null)
            {
                moneyTransferTransaction.ExternalTransaction.ExternalStatus = externalStatus;
                moneyTransferTransaction.ExternalTransaction.StatusDate = statusDate;
                moneyTransferTransaction.ExternalTransaction.StatusDescription = description;
                moneyTransferTransaction.ExternalTransaction.LastStatusDate = DateTime.Now;
            }

            //Evaluate Referral
            if (moneyTransferTransaction.Status == Status.SUCCESSFUL)
            {
                var userIsEligibleForReferralRewardResult = await this._referralProgramService.UserIsEligibleForReferralReward(moneyTransferTransaction);
                var isEligible = userIsEligibleForReferralRewardResult.Data.Item1;

                moneyTransferTransaction.UsedForReferral = userIsEligibleForReferralRewardResult.Data.Item2.UsedForReferral;

                if (isEligible)
                {
                    await _referralProgramService.RewardUser(moneyTransferTransaction.ReferralCode);
                }
            }

            if (moneyTransferTransaction.Status == Status.FAILED)
            {
                await this._referralProgramService.ReduceReferralCount(moneyTransferTransaction, lastUpdateDate);
            }

            // UAT ONLY
            if (moneyTransferTransaction.SendAmount == 10)
            {
                moneyTransferTransaction.Status = Status.PENDING;
                moneyTransferTransaction.Remarks = ConstantParam.PE_WaitingForSuspiciousTransactionAuthorizationError.ToString();
            }
            else if (moneyTransferTransaction.SendAmount == 11)
            {
                moneyTransferTransaction.Status = Status.PENDING;
                moneyTransferTransaction.Remarks = ConstantParam.PE_WaitingForCorrespondentBankAuthorizationError.ToString();
            }
            else if (moneyTransferTransaction.SendAmount == 12)
            {
                moneyTransferTransaction.Status = Status.PENDING;
            }
            else if (moneyTransferTransaction.SendAmount == 13)
            {
                moneyTransferTransaction.Status = Status.PENDING;
            }
            else if (moneyTransferTransaction.SendAmount == 14)
            {
                moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                moneyTransferTransaction.Remarks = ConstantParam.PE_InvalidBeneficiaryAccountNumberError.ToString();
            }
            else if (moneyTransferTransaction.SendAmount == 15)
            {
                moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                moneyTransferTransaction.Remarks = ConstantParam.PE_AccountFrozenError.ToString();
            }
            else if (moneyTransferTransaction.SendAmount == 16)
            {
                moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                moneyTransferTransaction.Remarks = ConstantParam.PE_IncorrectPurposeCodeError.ToString();
            }
            else if (moneyTransferTransaction.SendAmount == 17)
            {
                moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                moneyTransferTransaction.Remarks = ConstantParam.PE_InvalidIfsCodeError.ToString();
            }
            else if (moneyTransferTransaction.SendAmount == 18)
            {
                moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                moneyTransferTransaction.Remarks = ConstantParam.PE_BeneficiaryAccountClosedError.ToString();
            }
            else if (moneyTransferTransaction.SendAmount == 19)
            {
                moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                moneyTransferTransaction.Remarks = ConstantParam.PE_InvalidBeneficiaryNameError.ToString();
            }
            else if (moneyTransferTransaction.SendAmount == 20)
            {
                moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                moneyTransferTransaction.Remarks = ConstantParam.PE_BeneficiaryBankDidNotRespondError.ToString();
            }
            else if (moneyTransferTransaction.SendAmount == 21)
            {
                moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                moneyTransferTransaction.Remarks = ConstantParam.PE_StopPaymentDoneError.ToString();
            }
            else if (moneyTransferTransaction.SendAmount == 22)
            {
                moneyTransferTransaction.Status = Status.PENDINGREVERSE;
            }
            else if (moneyTransferTransaction.SendAmount == 23)
            {
                moneyTransferTransaction.Status = Status.FAILED;
            }
            else if (moneyTransferTransaction.SendAmount == 24)
            {
                moneyTransferTransaction.Status = Status.NEEDSMANUALREVERSAL;
            }
            // UAT ONLY

            await AddStatusProgress(moneyTransferTransaction, nameof(UpdateTransactionStatus));

            await _unitOfWork.CommitAsync();

            var status = moneyTransferTransaction.Status;

            if (status == initialStatus)
            {
            }
            else
            {

                if (status == Status.SUCCESSFUL || status == Status.FAILED)
                {
                    var profileId = moneyTransferTransaction.User.CardHolderId;
                    var amount = moneyTransferTransaction.SendAmount;
                    var beneficiary = moneyTransferTransaction.MoneyTransferBeneficiary;
                    var country = beneficiary.Country.Code3;
                    var beneficiaryName = beneficiary.FirstName;

                    var eventObject = new Tuple<string, MoneyTransferEvent, Status>(profileId, new MoneyTransferEvent()
                    {
                        Amount = amount,
                        BeneficiaryName = beneficiaryName,
                        Country = country,
                        ErrorMessage = status == Status.FAILED ? description : null
                    }, status);

                    var result = await this._analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                    {
                        eventObject
                    });

                    var cacheKey = _repeatTransferCacheKeyPrefix + moneyTransferTransaction.UserId.ToString();
                    await ClearCache(cacheKey);
                }
            }


            if (portalUserId != null)
            {
                await this._auditTrailService.AddAuditTrail(portalUserId, portalEmailId,
                    moneyTransferTransaction.UserId,
                    ConstantParam.AuditUpdateMoneyTransferTransactionStatus,
                    moneyTransferTransaction.ReferenceNumber);
            }

            return new ServiceResponse<MoneyTransferTransaction>(moneyTransferTransaction);
        }

        private async Task AddStatusProgress(MoneyTransferTransaction moneyTransferTransaction, string callerMethod)
        {
            moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

            // Add an entry to money transfer status progress.
            if (moneyTransferTransaction.Status == Status.SUCCESSFUL)
            {
                // Add new record or update existing one.
                // Only add new entries to this table if the last added one is not the same as the current one.
                var successfulStep = moneyTransferTransaction.MoneyTransferStatusSteps?.OrderBy(x => x.Timestamp).LastOrDefault();
                if (successfulStep is null || successfulStep.Status != Status.SUCCESSFUL)
                {
                    successfulStep = new MoneyTransferStatusStep() { Status = Status.SUCCESSFUL };
                    moneyTransferTransaction.MoneyTransferStatusSteps.Add(successfulStep);

                    // Call successful event.
                    await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                    {
                        new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                        {
                            Amount = moneyTransferTransaction.SendAmount,
                            BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                            Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                            ErrorMessage = ""
                        }, Status.SUCCESSFUL)
                    });
                }

                // Hide any old failed statuses.
                var failedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                foreach (var failedStep in failedSteps)
                {
                    failedStep.Hide = true;
                }

                successfulStep.Message = $"{ConstantParam.UM_SuccessfulTransfer}";
                successfulStep.ProviderRemarks = $"{moneyTransferTransaction.Remarks}";
                successfulStep.Log = $"{ConstantParam.UM_SuccessfulTransfer} - Called from: {callerMethod}";
            }
            else if (moneyTransferTransaction.Status == Status.PENDING)
            {
                // Add new record or update existing one.
                var pendingStep = moneyTransferTransaction.MoneyTransferStatusSteps?.OrderBy(x => x.Timestamp).LastOrDefault();
                if (pendingStep is null || pendingStep.Status != Status.PENDING)
                {
                    pendingStep = new MoneyTransferStatusStep() { Status = Status.PENDING };
                    moneyTransferTransaction.MoneyTransferStatusSteps.Add(pendingStep);

                    // Call pending event.
                    await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                    {
                        new Tuple<string, MoneyTransferEvent, Status>(moneyTransferTransaction.User.CardHolderId, new MoneyTransferEvent()
                        {
                            Amount = moneyTransferTransaction.SendAmount,
                            BeneficiaryName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName,
                            Country = moneyTransferTransaction.MoneyTransferBeneficiary.Country.Code3,
                            ErrorMessage = moneyTransferTransaction.Remarks
                        }, Status.PENDING)
                    });
                }

                // Hide any successful, failed steps.
                var successfulOrFailedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.SUCCESSFUL || s.Status == Status.FAILED);
                foreach (var successfulOrFailedStep in successfulOrFailedSteps)
                {
                    successfulOrFailedStep.Hide = true;
                }

                // Here we need to check the remarks of the transfer so that we can add a message for the user.
                switch (moneyTransferTransaction.Remarks)
                {
                    case ConstantParam.PE_WaitingForSuspiciousTransactionAuthorizationError:
                        pendingStep.ProviderRemarks = ConstantParam.PE_WaitingForSuspiciousTransactionAuthorizationError;
                        pendingStep.Message = ConstantParam.UM_WaitingForSuspiciousTransactionAuthorization;
                        pendingStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_WaitingForSuspiciousTransactionAuthorizationError}'";
                        break;
                    case ConstantParam.PE_WaitingForCorrespondentBankAuthorizationError:
                        pendingStep.ProviderRemarks = ConstantParam.PE_WaitingForCorrespondentBankAuthorizationError;
                        pendingStep.Message = ConstantParam.UM_WaitingForCorrespondentBankAuthorization;
                        pendingStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_WaitingForCorrespondentBankAuthorizationError}'";
                        break;
                    default:
                        pendingStep.ProviderRemarks = moneyTransferTransaction.Remarks;
                        pendingStep.Message = ConstantParam.UM_TransferPending;
                        pendingStep.Log = $"Step set to pending.";
                        break;
                }

                pendingStep.Log += $" - Called from: {callerMethod}";
            }
            else if (moneyTransferTransaction.Status == Status.PENDINGREVERSE)
            {
                // Add new record or update existing one.
                var pendingReverseStep = moneyTransferTransaction.MoneyTransferStatusSteps?.OrderBy(x => x.Timestamp).LastOrDefault();
                if (pendingReverseStep is null || pendingReverseStep.Status != Status.PENDINGREVERSE)
                {
                    pendingReverseStep = new MoneyTransferStatusStep() { Status = Status.PENDINGREVERSE };
                    moneyTransferTransaction.MoneyTransferStatusSteps.Add(pendingReverseStep);
                }

                // Hide any successful, failed steps.
                var successfulOrFailedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.SUCCESSFUL || s.Status == Status.FAILED);
                foreach (var successfulOrFailedStep in successfulOrFailedSteps)
                {
                    successfulOrFailedStep.Hide = true;
                }

                // Here we need to check the remarks of the transfer so that we can add a message for the user.
                switch (moneyTransferTransaction.Remarks)
                {
                    case ConstantParam.PE_InvalidBeneficiaryAccountNumberError:
                        pendingReverseStep.ProviderRemarks = ConstantParam.PE_InvalidBeneficiaryAccountNumberError;
                        pendingReverseStep.Message = ConstantParam.UM_InvalidBeneficiaryAccountNumber;
                        pendingReverseStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_InvalidBeneficiaryAccountNumberError}'";
                        break;
                    case ConstantParam.PE_AccountFrozenError:
                        pendingReverseStep.ProviderRemarks = ConstantParam.PE_AccountFrozenError;
                        pendingReverseStep.Message = ConstantParam.UM_AccountFrozen;
                        pendingReverseStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_AccountFrozenError}'";
                        break;
                    case ConstantParam.PE_IncorrectPurposeCodeError:
                        pendingReverseStep.ProviderRemarks = ConstantParam.PE_IncorrectPurposeCodeError;
                        pendingReverseStep.Message = ConstantParam.UM_IncorrectPurposeCode;
                        pendingReverseStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_IncorrectPurposeCodeError}'";
                        break;
                    case ConstantParam.PE_InvalidIfsCodeError:
                        pendingReverseStep.ProviderRemarks = ConstantParam.PE_InvalidIfsCodeError;
                        pendingReverseStep.Message = ConstantParam.UM_InvalidIfsCode;
                        pendingReverseStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_InvalidIfsCodeError}'";
                        break;
                    case ConstantParam.PE_BeneficiaryAccountClosedError:
                        pendingReverseStep.ProviderRemarks = ConstantParam.PE_BeneficiaryAccountClosedError;
                        pendingReverseStep.Message = ConstantParam.UM_BeneficiaryAccountClosed;
                        pendingReverseStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_BeneficiaryAccountClosedError}'";
                        break;
                    case ConstantParam.PE_InvalidBeneficiaryNameError:
                        pendingReverseStep.ProviderRemarks = ConstantParam.PE_InvalidBeneficiaryNameError;
                        pendingReverseStep.Message = ConstantParam.UM_InvalidBeneficiaryName;
                        pendingReverseStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_InvalidBeneficiaryNameError}'";
                        break;
                    case ConstantParam.PE_BeneficiaryBankDidNotRespondError:
                        pendingReverseStep.ProviderRemarks = ConstantParam.PE_BeneficiaryBankDidNotRespondError;
                        pendingReverseStep.Message = ConstantParam.UM_BeneficiaryBankDidNotRespond;
                        pendingReverseStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_BeneficiaryBankDidNotRespondError}'";
                        break;
                    case ConstantParam.PE_StopPaymentDoneError:
                        pendingReverseStep.ProviderRemarks = ConstantParam.PE_StopPaymentDoneError;
                        pendingReverseStep.Message = ConstantParam.UM_StopPaymentDone;
                        pendingReverseStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_StopPaymentDoneError}'";
                        break;
                    default:
                        pendingReverseStep.ProviderRemarks = moneyTransferTransaction.Remarks;
                        pendingReverseStep.Message = ConstantParam.UM_TransferFailedButWillBeReversed;
                        pendingReverseStep.Log = "Step set to pending reverse with no extra details.";
                        break;
                }

                pendingReverseStep.Log += $"Called from: {callerMethod}";
            }
            else if (moneyTransferTransaction.Status == Status.FAILED || moneyTransferTransaction.Status == Status.NEEDSMANUALREVERSAL)
            {
                // Add new record or update existing one.
                var failedStep = moneyTransferTransaction.MoneyTransferStatusSteps?.OrderBy(x => x.Timestamp).LastOrDefault();
                if (failedStep is null || failedStep.Status != Status.FAILED)
                {
                    failedStep = new MoneyTransferStatusStep() { Status = Status.FAILED };
                    moneyTransferTransaction.MoneyTransferStatusSteps.Add(failedStep);
                }

                // Hide any old successful statuses.
                var successfulSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.SUCCESSFUL);
                foreach (var successfulStep in successfulSteps)
                {
                    successfulStep.Hide = true;
                }

                failedStep.Message = ConstantParam.UM_TransferFailed;
                failedStep.ProviderRemarks = moneyTransferTransaction.Remarks;
                failedStep.Log = $"Step set to failed with no extra details. - Called from: {callerMethod}";
            }
            else if (moneyTransferTransaction.Status == Status.REVERSED)
            {
                // Add new record or update existing one.
                var reversedStep = moneyTransferTransaction.MoneyTransferStatusSteps?.OrderBy(x => x.Timestamp).LastOrDefault();
                if (reversedStep is null || reversedStep.Status != Status.REVERSED)
                {
                    reversedStep = new MoneyTransferStatusStep() { Status = Status.REVERSED };
                    moneyTransferTransaction.MoneyTransferStatusSteps.Add(reversedStep);
                }

                // Hide any successful, failed steps.
                var successfulOrFailedSteps = moneyTransferTransaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.SUCCESSFUL || s.Status == Status.FAILED);
                foreach (var successfulOrFailedStep in successfulOrFailedSteps)
                {
                    successfulOrFailedStep.Hide = true;
                }

                // Here we need to check the remarks of the transfer so that we can add a message for the user.
                switch (moneyTransferTransaction.Remarks)
                {
                    case ConstantParam.PE_InvalidBeneficiaryAccountNumberError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_InvalidBeneficiaryAccountNumberError;
                        reversedStep.Message = ConstantParam.UM_InvalidBeneficiaryAccountNumber_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_InvalidBeneficiaryAccountNumberError}'";
                        break;
                    case ConstantParam.PE_AccountFrozenError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_AccountFrozenError;
                        reversedStep.Message = ConstantParam.UM_AccountFrozen_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_AccountFrozenError}'";
                        break;
                    case ConstantParam.PE_IncorrectPurposeCodeError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_IncorrectPurposeCodeError;
                        reversedStep.Message = ConstantParam.UM_IncorrectPurposeCode_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_IncorrectPurposeCodeError}'";
                        break;
                    case ConstantParam.PE_InvalidIfsCodeError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_InvalidIfsCodeError;
                        reversedStep.Message = ConstantParam.UM_InvalidIfsCode_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_InvalidIfsCodeError}'";
                        break;
                    case ConstantParam.PE_BeneficiaryAccountClosedError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_BeneficiaryAccountClosedError;
                        reversedStep.Message = ConstantParam.UM_BeneficiaryAccountClosed_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_BeneficiaryAccountClosedError}'";
                        break;
                    case ConstantParam.PE_InvalidBeneficiaryNameError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_InvalidBeneficiaryNameError;
                        reversedStep.Message = ConstantParam.UM_InvalidBeneficiaryName_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_InvalidBeneficiaryNameError}'";
                        break;
                    case ConstantParam.PE_BeneficiaryBankDidNotRespondError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_BeneficiaryBankDidNotRespondError;
                        reversedStep.Message = ConstantParam.UM_BeneficiaryBankDidNotRespond_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_BeneficiaryBankDidNotRespondError}'";
                        break;
                    case ConstantParam.PE_StopPaymentDoneError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_StopPaymentDoneError;
                        reversedStep.Message = ConstantParam.UM_StopPaymentDone_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_StopPaymentDoneError}'";
                        break;
                    default:
                        reversedStep.ProviderRemarks = moneyTransferTransaction.Remarks;
                        reversedStep.Message = ConstantParam.UM_TransferReversed_R;
                        reversedStep.Log = "Step set to reversed with no extra details.";
                        break;
                }
            }
        }

        public async Task<ServiceResponse> BulkUpdateTransactionsStatuses(List<UpdateTransferStatusDetails> updates)
        {

            // Extract reference number from the list of updates to find the transactions we need to update.
            var referenceNumbers = updates.Select(s => s.ReferenceNumber).ToList();

            // Find transactions.
            _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Getting transactions from DB...");
            var moneyTransferTransactions = await _unitOfWork.MoneyTransferTransactions.FindAsync(x => referenceNumbers.Contains(x.ReferenceNumber),
                false,
                include => include.Transaction,
                include => include.ExternalTransaction,
                include => include.MoneyTransferBeneficiary,
                include => include.User,
                include => include.User.CardHolder,
                include => include.MoneyTransferBeneficiary.Country,
                include => include.MoneyTransferStatusSteps);

            // We need to keep a list of the events we need to fire after the processing is done.
            var events = new List<Tuple<string, MoneyTransferEvent, Status>>();

            // Update each transaction.
            foreach (var transactionUpdate in updates)
            {
                Log($"BulkUpdateTransactionsStatuses :: Working on transaction update: {transactionUpdate.ReferenceNumber}");

                // Find matching transaction.
                var moneyTransferTransaction = moneyTransferTransactions.FirstOrDefault(trn => trn.ReferenceNumber == transactionUpdate.ReferenceNumber);
                if (moneyTransferTransaction is null)
                {
                    _logger.LogWarning($"BulkUpdateTransactionsStatuses :: Unable to find matching transaction with reference number: {transactionUpdate.ReferenceNumber}.");
                    continue;
                }

                Log($"BulkUpdateTransactionsStatuses :: Found matching transaction: {moneyTransferTransaction.ReferenceNumber}.");

                //For checking Rewards and Event
                moneyTransferTransaction.InitialStatus = moneyTransferTransaction.Status;

                if (moneyTransferTransaction.ExternalTransaction == null)
                {

                    moneyTransferTransaction.ExternalTransaction = new MoneyTransferExternalTransaction()
                    {
                        ExternalStatus = transactionUpdate.RAKStatus,
                        StatusDescription = transactionUpdate.Remarks,
                        MessageId = null,
                        ExternalTransactionId = transactionUpdate.ExternalReferenceNumber,
                        StartDate = DateTime.Now.Date,
                        EndDate = DateTime.Now.Date,
                        Type = TransactionType.RAKMoney.ToString(),
                        ChargesAmount = transactionUpdate.Charge,
                        ChargesCurrency = moneyTransferTransaction.ChargesCurrency,
                        WaivedCharge = moneyTransferTransaction.WaivedCharge,
                        TotalCharges = moneyTransferTransaction.TotalCharges,
                        TotalCreditAmount = moneyTransferTransaction.ReceiveAmount,
                        TotalDebitAmount = transactionUpdate.Amount,
                        CreditCurrency = moneyTransferTransaction.ReceiveCurrency,
                        DebitCurrency = moneyTransferTransaction.SendCurrency,
                        StatusDate = transactionUpdate.StatusDate,
                        LastStatusDate = DateTime.Now
                    };

                    await _unitOfWork.MoneyTransferExternalTransactions.AddAsync(moneyTransferTransaction.ExternalTransaction);
                }
                else if (moneyTransferTransaction.ExternalTransaction.ExternalStatus != transactionUpdate.RAKStatus ||
                         moneyTransferTransaction.ExternalTransaction.StatusDescription != transactionUpdate.Remarks)
                {
                    // 'ExternalTransaction' is not null
                    _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Updating 'ExternalTransaction' for Transaction: {moneyTransferTransaction.ReferenceNumber}.");

                    // Update external transaction properties.
                    moneyTransferTransaction.ExternalTransaction.ExternalStatus = transactionUpdate.RAKStatus;
                    moneyTransferTransaction.ExternalTransaction.StatusDate = transactionUpdate.StatusDate;
                    moneyTransferTransaction.ExternalTransaction.StatusDescription = transactionUpdate.Remarks;
                    moneyTransferTransaction.ExternalTransaction.LastStatusDate = DateTime.Now;
                }
                else
                {
                    _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Updating 'ExternalTransaction' for transaction: {moneyTransferTransaction.ReferenceNumber} skipped");
                    continue;
                }

                // Keep a copy of the transaction status to make sure it was changed.
                var initialStatus = moneyTransferTransaction.Status;
                var newStatus = transactionUpdate.Status;

                if (newStatus == Status.PENDINGREVERSE && initialStatus != Status.REVERSED && initialStatus != Status.FAILEDTOREVERSE)
                {
                    if (moneyTransferTransaction.CreatedDate < _moneyTransferServiceSettings.ReversalStartDate)
                    {
                        _logger.LogDebug($"UpdatePendingTransactions :: Created date is less than reversal start date, reference number {moneyTransferTransaction.ReferenceNumber}.");
                        moneyTransferTransaction.Status = Status.FAILED;
                    }
                    else if (allowRemarksForReversal.Any(transactionUpdate.Remarks.StartsWith))
                    {
                        moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                    }
                    else
                    {
                        switch (_moneyTransferServiceSettings.ReversalMode)
                        {
                            case ReversalMode.None:
                                _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Reversal is off, reference number {moneyTransferTransaction.ReferenceNumber}.");
                                moneyTransferTransaction.Status = Status.FAILED;
                                break;
                            case ReversalMode.EdenredCorporateIds:
                                // Check if this transaction was done by us.
                                if (moneyTransferTransaction.User.CardHolder.CorporateId == "99999" || moneyTransferTransaction.User.CardHolder.CorporateId == "99998")
                                {
                                    _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Transaction belongs to edenred, marking it as PendingReversal, reference number {moneyTransferTransaction.ReferenceNumber}.");
                                    moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                                }
                                else
                                {
                                    _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Reversal is on only for edenred, reference number {moneyTransferTransaction.ReferenceNumber}.");
                                    moneyTransferTransaction.Status = Status.FAILED;
                                }
                                break;
                            case ReversalMode.All:
                                _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Reversal is on, marking transaction as PendingReverse {moneyTransferTransaction.ReferenceNumber}.");
                                moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                                break;
                            default:
                                break;
                        }
                    }
                }
                else if (nonUpdatableStatuses.Contains(initialStatus) == false)
                {
                    moneyTransferTransaction.Status = newStatus; // 'transaction.Status' now has the updated value from the report.
                }

                // Update transaction properties.
                _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Updating properties for transaction: {moneyTransferTransaction.ReferenceNumber}.");

                moneyTransferTransaction.Remarks = transactionUpdate.Remarks;

                await AddStatusProgress(moneyTransferTransaction, nameof(BulkUpdateTransactionsStatuses));
            }

            _logger.LogDebug("BulkUpdateTransactionsStatuses :: Updating transaction commits to db.");

            // Save changes made to the properties.
            await _unitOfWork.CommitAsync();


            foreach (var moneyTransferTransaction in moneyTransferTransactions)
            {
                // If the transaction status is successful => start the Rewards Programs pipeline.
                if (moneyTransferTransaction.Status == Status.SUCCESSFUL)
                {
                    _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Transaction: {moneyTransferTransaction.ReferenceNumber} is successful.");

                    // Check and apply rewards.
                    await CheckAndApplyRewards(moneyTransferTransaction); // SaveChanges is called in this method.
                }

                // Checking if the status was changed.
                var status = moneyTransferTransaction.Status;
                var initialStatus = moneyTransferTransaction.InitialStatus;

                _logger.LogDebug(ConstantParam.EvaluatingMoneyTransferEvent, (int)status, status.ToString(), (int)initialStatus, initialStatus.ToString());

                if (status == initialStatus)
                {
                    _logger.LogDebug(ConstantParam.UploadingMoneyTransferEventSkipped, (int)status, status.ToString(), (int)initialStatus, initialStatus.ToString());
                }
                else
                {
                    _logger.LogDebug(ConstantParam.EvaluatingMoneyTransferEventFinished, (int)status, status.ToString(), (int)initialStatus, initialStatus.ToString());

                    if (status == Status.SUCCESSFUL || status == Status.FAILED)
                    {
                        var profileId = moneyTransferTransaction.User.CardHolderId;
                        var amount = moneyTransferTransaction.SendAmount;
                        var beneficiary = moneyTransferTransaction.MoneyTransferBeneficiary;
                        var country = beneficiary.Country.Code3;
                        var beneficiaryName = beneficiary.FirstName;

                        var eventObject = new Tuple<string, MoneyTransferEvent, Status>(profileId, new MoneyTransferEvent()
                        {
                            Amount = amount,
                            BeneficiaryName = beneficiaryName,
                            Country = country,
                            ErrorMessage = status == Status.FAILED ? moneyTransferTransaction.Remarks : null
                        }, status);

                        events.Add(eventObject);

                        var cacheKey = _repeatTransferCacheKeyPrefix + moneyTransferTransaction.UserId.ToString();
                        await ClearCache(cacheKey);
                    }
                }
            }

            _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Publishing MoneyTransferEvents");

            await _analyticsPublisherService.PublishMoneyTransferEvents(events);

            _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Published MoneyTransferEvents");

            return new ServiceResponse();
        }

        private async Task CheckAndApplyRewards(MoneyTransferTransaction transaction)
        {
            // Check if user is eligible for a reward.
            Log($"BulkUpdateTransactionsStatuses :: Checking for rewards towards transaction: {transaction.ReferenceNumber}.");

            var tryGetRewardEligibilty = await this._referralProgramService.UserIsEligibleForReferralReward(transaction);

            transaction.UsedForReferral = tryGetRewardEligibilty.Data.Item2.UsedForReferral;
            var isEligible = tryGetRewardEligibilty.Data.Item1;

            if (isEligible)
            {
                Log($"BulkUpdateTransactionsStatuses :: Reward is approved for transaction: {transaction.ReferenceNumber}.");
                _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Referal code is: {transaction.ReferralCode}.");
                await _referralProgramService.RewardUser(transaction.ReferralCode);
            }
            else
            {
                Log($"BulkUpdateTransactionsStatuses :: Reward is not approved for transaction: {transaction.ReferenceNumber}.");
            }

            // Save changes (property updates and rewards.)
            await _unitOfWork.CommitAsync();
        }

        [Obsolete]
        private async Task DoRedemptionReversal(Guid TransactionId)
        {
            // read transaction
            MoneyTransferTransaction mttransaction = await _unitOfWork.MoneyTransferTransactions.FirstOrDefaultAsync(x => x.Id == TransactionId, x => x.Transaction, x => x.User, x => x.User.CardHolder);

            if (string.IsNullOrEmpty(mttransaction.Transaction.ReferenceNumber))
            {
                _logger.LogWarning($"Reference Id missed for the Redemption reversal of transaction id : {mttransaction.ReferenceNumber}");

                return;
            }

            // PPS call for reversal 
            var transactionDebitAmount = (mttransaction.SendAmount + mttransaction.TotalCharges.GetValueOrDefault());

            var narration = string.Join(" ", EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.MoneyTransfer), "Reversal");

            var ppsCreditAmount = transactionDebitAmount * 100;
            var belongsToExchangeHouse = mttransaction.User.CardHolder.BelongsToExchangeHouse;

            var reversalRequest = new RedemptionReversalRequest()
            {
                Amount = decimal.Truncate(ppsCreditAmount),
                CardPanNumber = mttransaction.Transaction.CardNumber,
                CardSerialNumber = mttransaction.Transaction.CardSerialNumber,
                RedemptionId = mttransaction.Transaction.ReferenceNumber,
                Narration = narration,
                Description = narration,
                ReferenceNumber = mttransaction.Transaction.ReferenceNumber,
                TerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, mttransaction.User.ApplicationId, TransactionMerchantCodeFeature.MoneyTransfer),
                MerchantLoopCode = "5000"
            };

            var redemptionReversalResponse = await _ppsWebAuthService.DoRedemptionReversal(reversalRequest);

            if (!redemptionReversalResponse.IsSuccessful)
            {
                _logger.LogError($"Redemption reversal failed for  the transaction : {mttransaction.ReferenceNumber} and the Reference Id :{mttransaction.Transaction.ReferenceNumber} ");

                mttransaction.Transaction.Reversal.StatusCode = BaseEnums.Status.FAILED.ToString();
                mttransaction.Transaction.Reversal.StatusDescription = "Reversal failed";
            }
            else
            {

                mttransaction.Transaction.StatusCode = redemptionReversalResponse.Data.StatusCode;
                mttransaction.Transaction.Reversal.AuthenticationCode = redemptionReversalResponse.Data.AuthCode;
                mttransaction.Transaction.Reversal.Amount = TypeUtility.GetDecimalFromString(redemptionReversalResponse.Data.Value.Amt);
                mttransaction.Transaction.Reversal.EndBalance = TypeUtility.GetDecimalFromString(redemptionReversalResponse.Data.EndBalanace.Amt);
                mttransaction.Transaction.Reversal.Date = DateTime.Now;
                mttransaction.Transaction.Reversal.ReferenceNumber = reversalRequest.ReferenceNumber;
                mttransaction.Transaction.Reversal.StatusCode = redemptionReversalResponse.Data.StatusCode;
                mttransaction.Transaction.Reversal.StatusDescription = redemptionReversalResponse.Data.Message;
            }
            await _unitOfWork.CommitAsync();
        }

        public async Task<ServiceResponse<Tuple<IList<MoneyTransferTransaction>, int>>> SearchUserMoneyTransferTransactionsReadOnly(Guid id, SearchBeneficiaryParameters searchBeneficiaryParameters)
        {
            var filters = MoneyTransferTransactionFilter.GetFilterById(id, searchBeneficiaryParameters);
            var responseBeneficiary = await this._unitOfWorkReadOnly.MoneyTransferTransactions.FindAsync(filters, x => x.CreatedDate, true, searchBeneficiaryParameters.Page.HasValue ? (searchBeneficiaryParameters.Page - 1) * searchBeneficiaryParameters.Size : null, searchBeneficiaryParameters.Size.HasValue ? searchBeneficiaryParameters.Size : null, true, x => x.MoneyTransferBeneficiary, x => x.ExternalTransaction, x => x.MoneyTransferBeneficiary.LinkedUser);
            return new ServiceResponse<Tuple<IList<MoneyTransferTransaction>, int>>(responseBeneficiary);
        }

        public async Task<ServiceResponse<Tuple<List<MoneyTransferTransactionStruct>, int>>> SearchMoneyTransferTransactionsReadOnly(SearchBeneficiaryParameters searchBeneficiaryParameters)
        {
            var filters = MoneyTransferTransactionFilter.GetMoneyTransactionFilter(searchBeneficiaryParameters);
            var responseBeneficiary = await this._unitOfWorkReadOnly.MoneyTransferTransactions.Search(filters, searchBeneficiaryParameters.Page, searchBeneficiaryParameters.Size);
            return new ServiceResponse<Tuple<List<MoneyTransferTransactionStruct>, int>>(responseBeneficiary);
        }

        public async Task<ServiceResponse<Tuple<IList<MoneyTransferTransaction>, int>>> SearchC3toC3Transactions(Guid id, SearchBeneficiaryParameters searchBeneficiaryParameters)
        {
            var filters = MoneyTransferTransactionFilter.GetC3toC3TransactionFilter(id, searchBeneficiaryParameters);
            var responseTransactions = await this._unitOfWork.MoneyTransferTransactions.FindAsync(filters, x => x.CreatedDate, true, searchBeneficiaryParameters.Page.HasValue ? (searchBeneficiaryParameters.Page - 1) * searchBeneficiaryParameters.Size : null, searchBeneficiaryParameters.Size.HasValue ? searchBeneficiaryParameters.Size : null, true, x => x.User, x => x.User.CardHolder, x => x.MoneyTransferBeneficiary, x => x.ExternalTransaction, x => x.Transaction, x => x.MoneyTransferBeneficiary.LinkedUser);
            return new ServiceResponse<Tuple<IList<MoneyTransferTransaction>, int>>(responseTransactions);
        }

        public async Task<ServiceResponse> RefreshRates()
        {
            var rateExpiryInMinutes = _moneyTransferServiceSettings.RateExpiryInMinutes;

            // Keep track of old corridors that we got rates for.
            // We don't want to get the rates for them more than once.
            var oldCorridorsRatesCheck = new List<string>();

            // Get all old corridors.
            var oldCorridors = await _unitOfWork.Countries.FindAsync(c => c.MoneyTransferEnabled && c.IsPapularCountry, false);

            var dateTime = DateTime.Now;
            // Get all new corridors.
            var newCorridors = await _unitOfWork.MoneyTransferMethods.FindAsync(x => x.MoneyTransferProvider.RateExpiryDate <= dateTime, false, i => i.MoneyTransferProvider);

            // Loop new corridors.
            foreach (var transferMethod in newCorridors)
            {
                if (transferMethod.MoneyTransferProvider.WUGetRatesEnabled)
                {
                    // Get rate from new service.
                    var request = new GetFxRateRequest()
                    {
                        EmiratesId = this._moneyTransferServiceSettings.RefreshRatesEmiratesId,

                        BaseCurrency = ConstantParam.DefaultCurrency,
                        TargetCurrency = transferMethod.MoneyTransferProvider.Currency,

                        ExchangeAmount = new ExchangeAmount()
                        {
                            Amount = "1",
                            Currency = ConstantParam.DefaultCurrency
                        },

                        BankCountryCode = transferMethod.MoneyTransferProvider.CountryCode,
                        CalculateCharges = true
                    };

                    // Cast enum since we have two enum types with the same name.
                    if (transferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer)
                    {
                        request.MoneyTransferMethodType = Enums.MoneyTransferMethodType.BankTransfer;
                    }
                    else if (transferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.CashPickup)
                    {
                        request.MoneyTransferMethodType = Enums.MoneyTransferMethodType.CashPickup;
                    }
                    else if (transferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.Wallet)
                    {
                        request.MoneyTransferMethodType = Enums.MoneyTransferMethodType.Wallet;
                    }

                    var tryGetFxRate = await _externalProviderMoneyTransferService.GetFxRate(request);
                    if (tryGetFxRate.IsSuccessful == false)
                    {
                        continue;
                    }

                    var rateResult = tryGetFxRate.Data.FxRateConversions.First();

                    // Update in new tables.
                    transferMethod.MoneyTransferProvider.Rate = Math.Round(1 / Convert.ToDecimal(tryGetFxRate.Data.FxRateConversions.First().Rate), 5);
                    transferMethod.MoneyTransferProvider.RateExpiryDate = DateTime.Now.AddMinutes(rateExpiryInMinutes);

                    // Update in old tables.
                    var oldCorridor = oldCorridors.FirstOrDefault(x => x.Code == transferMethod.MoneyTransferProvider.CountryCode);
                    if (oldCorridor != null)
                    {
                        if (transferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer)
                        {
                            oldCorridorsRatesCheck.Add($"{oldCorridor.Code}-{MoneyTransferMethodType.BankTransfer}");
                            oldCorridor.BankTransferLatestRate = Convert.ToDecimal(tryGetFxRate.Data.FxRateConversions.First().Rate);
                            oldCorridor.RatesLastUpdatedDate = DateTime.Now.AddMinutes(rateExpiryInMinutes);
                        }
                        if (transferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.CashPickup)
                        {
                            oldCorridorsRatesCheck.Add($"{oldCorridor.Code}-{MoneyTransferMethodType.CashPickup}");
                            oldCorridor.CashPickUpLatestRate = Convert.ToDecimal(tryGetFxRate.Data.FxRateConversions.First().Rate);
                            oldCorridor.RatesLastUpdatedDate = DateTime.Now.AddMinutes(rateExpiryInMinutes);
                        }
                    }
                }
                else
                {
                    // Rate is not enabled for this corridor.
                    // Check if this corridor is part of the old list. If so, get the rate for this corridor using the old implementation.
                    // If the type is wallet, then continue because we didn't have this transfer method before.
                    if (transferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.Wallet)
                    {
                        continue;
                    }

                    var oldCorridor = oldCorridors.FirstOrDefault(x => x.Code == transferMethod.MoneyTransferProvider.CountryCode);
                    if (oldCorridor is null)
                    {
                        // Here we have a new corridor, continue.
                        continue;
                    }

                    // Here, we have an old corridor.
                    var request = new FxRateRequestRakModel();

                    // Add this corridor to the tracking list.
                    if (transferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer && oldCorridor.EligibleForBankTransfer)
                    {
                        oldCorridorsRatesCheck.Add($"{oldCorridor.Code}-{MoneyTransferMethodType.BankTransfer}");
                        request.TransferType = MoneyTransferType.OutsideUAE.ToString();
                    }
                    if (transferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.CashPickup && oldCorridor.EligibleForCashPickUp)
                    {
                        oldCorridorsRatesCheck.Add($"{oldCorridor.Code}-{MoneyTransferMethodType.CashPickup}");
                        request.TransferType = MoneyTransferType.RAKMoneyCashPayout.ToString();
                    }

                    // Get rate for this old corridor using the old implementation.
                    request.TransactionType = TransactionType.RAKMoney.ToString();
                    request.FromCurrency = ConstantParam.DefaultCurrency;
                    request.ToCurrency = oldCorridor.Currency;
                    request.Fxvalue = new FxValueRakModel()
                    {
                        Amount = "1",
                        Currency = ConstantParam.DefaultCurrency
                    };
                    request.BankCountry = oldCorridor.Code;
                    request.BeneficiaryId = null;
                    request.Charges = true;

                    var tryGetFxRate = await _rakService.GetFxRate(new List<FxRateRequestRakModel>() { request }, this._moneyTransferServiceSettings.RefreshRatesEmiratesId);
                    if (tryGetFxRate.IsSuccessful == false)
                    {
                        continue;
                    }

                    // Update in old tables.
                    if (transferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer)
                    {
                        oldCorridor.BankTransferLatestRate = Convert.ToDecimal(tryGetFxRate.Data.First().FxConversionRates.First().Rate);
                        oldCorridor.RatesLastUpdatedDate = DateTime.Now.AddMinutes(rateExpiryInMinutes);
                    }
                    if (transferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.CashPickup)
                    {
                        oldCorridor.CashPickUpLatestRate = Convert.ToDecimal(tryGetFxRate.Data.First().FxConversionRates.First().Rate);
                        oldCorridor.RatesLastUpdatedDate = DateTime.Now.AddMinutes(rateExpiryInMinutes);
                    }

                    // Update in new tables (only for non wallet transfer methods).
                    transferMethod.MoneyTransferProvider.Rate = Math.Round(1 / Convert.ToDecimal(tryGetFxRate.Data.First().FxConversionRates.First().Rate), 5);
                    transferMethod.MoneyTransferProvider.RateExpiryDate = DateTime.Now;
                }
            }

            await _unitOfWork.CommitAsync();
            await ClearCache(_remittanceDestinationCacheKey);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> UpdatePendingTransactions()
        {
            _logger.LogDebug($"UpdatePendingTransactions :: Started...");

            // Find pending transactions (based on a starting date.)
            var startDate = DateTime.Now.Date.AddDays(_moneyTransferServiceSettings.PendingSchedulerMinNoOfDays);
            _logger.LogDebug($"UpdatePendingTransactions :: Settings: MinStartDate: {startDate}");

            var dateNow = DateTime.Now.AddMinutes(-5);

            var pendingTransactions = await this._unitOfWork.MoneyTransferTransactions.FindAsync(
                t => t.Status == Status.PENDING
                && t.ExternalTransaction != null
                && t.ReferenceNumber != null
                && t.TransferType == TransactionType.RAKMoney
                && t.CreatedDate > startDate
                && t.CreatedDate < dateNow,
                false,
                include => include.MoneyTransferBeneficiary,
                include => include.ExternalTransaction,
                include => include.Transaction,
                include => include.User,
                include => include.MoneyTransferBeneficiary.Country,
                include => include.User.CardHolder);

            Log($"UpdatePendingTransactions :: Pending Transactions Found : {pendingTransactions.Count} transactions.");

            // We need to keep a list of the events we need to fire after the processing is done.
            var events = new List<Tuple<string, MoneyTransferEvent, Status>>();

            foreach (var transaction in pendingTransactions)
            {
                _logger.LogDebug($"UpdatePendingTransactions :: Working on transaction {transaction.ReferenceNumber}.");

                // Call API to get the latest status for this transactions.
                _logger.LogDebug($"UpdatePendingTransactions :: Calling API for transaction {transaction.ReferenceNumber} to get the latest status.");

                ServiceResponse<MoneyTransferResponseRakModel> tryGetLatestStatus = null;
                try
                {
                    //Rak API call to get status of transaction
                    tryGetLatestStatus = await _rakService.GetMoneyTransferTransactionStatus(transaction.ReferenceNumber.ToString(), transaction.User.CardHolder.EmiratesId, TransactionType.RAKMoney.ToString());
                    if (tryGetLatestStatus.IsSuccessful == false)
                    {
                        _logger.LogWarning($"UpdatePendingTransactions :: Failed to get latest status for transaction {transaction.ReferenceNumber}. Error: {tryGetLatestStatus.ErrorMessage}");
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"UpdatePendingTransactions :: Unable to get Status for transaction: {transaction.ReferenceNumber} due to error {ex.Message}.");
                    continue;
                }

                _logger.LogDebug($"UpdatePendingTransactions :: Status for transaction: {transaction.ReferenceNumber} is {tryGetLatestStatus.Data.StatusDesc}.");

                // Keep a copy of the transaction status to make sure it was changes.
                var initialStatus = transaction.Status;
                transaction.InitialStatus = initialStatus;

                if (transaction.ExternalTransaction.ExternalStatus == tryGetLatestStatus.Data.Status &&
                    transaction.Remarks == tryGetLatestStatus.Data.StatusDesc)
                {
                    Log($"UpdatePendingTransactions :: Updating for transaction: {transaction.ReferenceNumber} skipped");
                    continue;
                }

                // Update transaction properties.
                transaction.Remarks = tryGetLatestStatus.Data.StatusDesc;
                transaction.UpdatedDate = DateTime.Now;

                transaction.ExternalTransaction.ExternalStatus = tryGetLatestStatus.Data.Status;
                transaction.ExternalTransaction.StatusDate = DateTime.Now;
                transaction.ExternalTransaction.StatusDescription = tryGetLatestStatus.Data.StatusDesc;
                transaction.ExternalTransaction.LastStatusDate = DateTime.Now;

                var newStatus = Status.PENDING;

                // SIMULATION!!1!! DONT ADD THIS TO PROD PLZZZ :'(
                if (transaction.SendAmount == 31)
                {
                    tryGetLatestStatus.Data.Status = nameof(ExternalStatus.REV);
                    Log($"UpdatePendingTransactions :: Mock transaction {transaction.ReferenceNumber} no be PENDINGREVERSE.");
                }
                // END OF SIMULATION

                // Update transaction status.
                switch (tryGetLatestStatus.Data.Status)
                {
                    case nameof(ExternalStatus.S):
                        newStatus = Status.SUCCESSFUL;
                        break;

                    case nameof(ExternalStatus.F):
                        newStatus = Status.FAILED;
                        break;

                    case nameof(ExternalStatus.REJ):
                    case nameof(ExternalStatus.REV):
                        if (transaction.CreatedDate < _moneyTransferServiceSettings.ReversalStartDate)
                        {
                            _logger.LogDebug($"UpdatePendingTransactions :: Created date is less than reversal start date, reference number {transaction.ReferenceNumber}.");
                            newStatus = Status.FAILED;
                        }
                        else if (allowRemarksForReversal.Any(transaction.Remarks.StartsWith))
                        {
                            newStatus = Status.PENDINGREVERSE;
                        }
                        else
                        {
                            switch (_moneyTransferServiceSettings.ReversalMode)
                            {
                                case ReversalMode.None:
                                    _logger.LogDebug($"UpdatePendingTransactions :: Reversal is off, reference number {transaction.ReferenceNumber}.");
                                    newStatus = Status.FAILED;
                                    break;
                                case ReversalMode.EdenredCorporateIds:
                                    // Check if this transaction was done by us.
                                    if (transaction.User.CardHolder.CorporateId == "99999" || transaction.User.CardHolder.CorporateId == "99998")
                                    {
                                        _logger.LogDebug($"UpdatePendingTransactions :: Transaction belongs to edenred, marking it as PendingReversal, reference number {transaction.ReferenceNumber}.");
                                        newStatus = Status.PENDINGREVERSE;
                                    }
                                    else
                                    {
                                        _logger.LogDebug($"UpdatePendingTransactions :: Reversal is on only for edenred, reference number {transaction.ReferenceNumber}.");
                                        newStatus = Status.FAILED;
                                    }
                                    break;
                                case ReversalMode.All:
                                    _logger.LogDebug($"UpdatePendingTransactions :: Reversal is on, marking transaction as PendingReverse {transaction.ReferenceNumber}.");
                                    newStatus = Status.PENDINGREVERSE;
                                    break;
                                default:
                                    break;
                            }
                        }
                        break;
                    case nameof(ExternalStatus.P):
                        newStatus = Status.PENDING;
                        break;

                    default:
                        _logger.LogWarning($"UpdatePendingTransactions :: Unable to parse status for transaction {transaction.ReferenceNumber}. Status: {tryGetLatestStatus.Data.Status}");
                        break;
                }

                if (!nonUpdatableStatuses.Contains(initialStatus))
                {
                    transaction.Status = newStatus;
                }

                // UAT ONLY
                if (transaction.SendAmount == 110)
                {
                    transaction.Status = Status.PENDING;
                    transaction.Remarks = ConstantParam.PE_WaitingForSuspiciousTransactionAuthorizationError.ToString();
                }
                else if (transaction.SendAmount == 111)
                {
                    transaction.Status = Status.PENDING;
                    transaction.Remarks = ConstantParam.PE_WaitingForCorrespondentBankAuthorizationError.ToString();
                }
                else if (transaction.SendAmount == 112)
                {
                    transaction.Status = Status.PENDING;
                }
                else if (transaction.SendAmount == 113)
                {
                    transaction.Status = Status.PENDING;
                }
                else if (transaction.SendAmount == 114)
                {
                    transaction.Status = Status.PENDINGREVERSE;
                    transaction.Remarks = ConstantParam.PE_InvalidBeneficiaryAccountNumberError.ToString();
                }
                else if (transaction.SendAmount == 115)
                {
                    transaction.Status = Status.PENDINGREVERSE;
                    transaction.Remarks = ConstantParam.PE_AccountFrozenError.ToString();
                }
                else if (transaction.SendAmount == 116)
                {
                    transaction.Status = Status.PENDINGREVERSE;
                    transaction.Remarks = ConstantParam.PE_IncorrectPurposeCodeError.ToString();
                }
                else if (transaction.SendAmount == 117)
                {
                    transaction.Status = Status.PENDINGREVERSE;
                    transaction.Remarks = ConstantParam.PE_InvalidIfsCodeError.ToString();
                }
                else if (transaction.SendAmount == 118)
                {
                    transaction.Status = Status.PENDINGREVERSE;
                    transaction.Remarks = ConstantParam.PE_BeneficiaryAccountClosedError.ToString();
                }
                else if (transaction.SendAmount == 119)
                {
                    transaction.Status = Status.PENDINGREVERSE;
                    transaction.Remarks = ConstantParam.PE_InvalidBeneficiaryNameError.ToString();
                }
                else if (transaction.SendAmount == 120)
                {
                    transaction.Status = Status.PENDINGREVERSE;
                    transaction.Remarks = ConstantParam.PE_BeneficiaryBankDidNotRespondError.ToString();
                }
                else if (transaction.SendAmount == 121)
                {
                    transaction.Status = Status.PENDINGREVERSE;
                    transaction.Remarks = ConstantParam.PE_StopPaymentDoneError.ToString();
                }
                else if (transaction.SendAmount == 122)
                {
                    transaction.Status = Status.PENDINGREVERSE;
                }
                else if (transaction.SendAmount == 123)
                {
                    transaction.Status = Status.FAILED;
                }
                else if (transaction.SendAmount == 124)
                {
                    transaction.Status = Status.NEEDSMANUALREVERSAL;
                }
                // UAT ONLY

                await AddStatusProgress(transaction, nameof(UpdatePendingTransactions));
            }


            // Save changes made to the properties.
            await this._unitOfWork.CommitAsync();


            foreach (var transaction in pendingTransactions)
            {
                // If the transaction status is successful => start the Rewards Programs pipeline.
                if (transaction.Status == Status.SUCCESSFUL)
                {

                    // Check and apply rewards.
                    await CheckAndApplyRewards(transaction); // SaveChanges is called in this method.


                    // Send a message to notify that the transact
                    try
                    {

                    }
                    catch (Exception ex)
                    {

                        throw;
                    }
                }

                // If the transaction status is failed => Reduce referral count.
                else if (transaction.Status == Status.FAILED)
                {
                    await this._referralProgramService.ReduceReferralCount(transaction, transaction.UpdatedDate ?? transaction.CreatedDate);
                    await this._unitOfWork.CommitAsync();
                }

                var initialStatus = transaction.InitialStatus;

                if (transaction.Status == initialStatus)
                {
                    _logger.LogDebug(ConstantParam.UploadingMoneyTransferEventSkipped, (int)transaction.Status, transaction.Status.ToString(), (int)initialStatus, initialStatus.ToString());
                }
                else
                {
                    _logger.LogDebug(ConstantParam.EvaluatingMoneyTransferEventFinished, (int)transaction.Status, transaction.Status.ToString(), (int)initialStatus, initialStatus.ToString());

                    if (transaction.Status == Status.SUCCESSFUL || transaction.Status == Status.FAILED)
                    {
                        var profileId = transaction.User.CardHolderId;
                        var amount = transaction.SendAmount;
                        var beneficiary = transaction.MoneyTransferBeneficiary;
                        var country = beneficiary.Country.Code3;
                        var beneficiaryName = beneficiary.FirstName;

                        var eventObject = new Tuple<string, MoneyTransferEvent, Status>(profileId, new MoneyTransferEvent()
                        {
                            Amount = amount,
                            BeneficiaryName = beneficiaryName,
                            Country = country,
                            ErrorMessage = transaction.Status == Status.FAILED ? transaction.Remarks : null
                        }, transaction.Status);

                        events.Add(eventObject);
                    }

                    _logger.LogDebug($"UpdatePendingTransactions :: Completed updating status for transaction {transaction.ReferenceNumber}.");
                }
            }

            _logger.LogDebug($"UpdatePendingTransactions :: Publishing MoneyTransferEvents");

            await _analyticsPublisherService.PublishMoneyTransferEvents(events);

            Log($"UpdatePendingTransactions :: Published MoneyTransferEvents");

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> BulkReverseTransactions(List<ReversalAmount> reversals)
        {

            // Extract reference number from the list of updates to find the transactions we need to update.
            var referenceNumbers = reversals.Select(s => s.ReferenceNumber).ToList();

            // Find transactions.
            _logger.LogDebug($"BulkReverseTransactions :: Getting transactions from DB...");
            var moneyTransferTransactions = await _unitOfWork.MoneyTransferTransactions.FindAsync(x => referenceNumbers.Contains(x.ReferenceNumber),
                false,
                include => include.Transaction,
                include => include.ExternalTransaction,
                include => include.MoneyTransferBeneficiary,
                include => include.User,
                include => include.User.CardHolder,
                include => include.MoneyTransferBeneficiary.Country,
                include => include.MoneyTransferStatusSteps);

            foreach (var reversal in reversals)
            {
                var transaction = moneyTransferTransactions.FirstOrDefault(f => f.ReferenceNumber == reversal.ReferenceNumber);
                if (transaction != null)
                {
                    await ReverseTransaction(transaction, reversal.Amount);
                }
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> ReverseFailedMoneyTransferTransactions()
        {
            _logger.LogDebug($"ReverseFailedBankTransactions :: Checking if reversals are enabled.");

            var reversalEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableReversal);
            if (!reversalEnabled)
            {
                return new ServiceResponse();
            }


            // Find transactions that are marked as PendingReverse.
            var pendingReverseTransactions = await this._unitOfWork.MoneyTransferTransactions.GetReversableTransactions(this._moneyTransferServiceSettings.ReversalMode);

            foreach (var transaction in pendingReverseTransactions)
            {
                await ReverseTransaction(transaction);
            }

            return new ServiceResponse();
        }

        private async Task ReverseTransaction(MoneyTransferTransaction transaction, decimal reversalAmount = 0)
        {
            Log($"ReverseFailedBankTransactions :: Working on transaction {transaction.ReferenceNumber}.");
            _logger.LogDebug($"ReverseFailedBankTransactions :: Transaction status is: {transaction.Status}.");

            // Start reversal pipeline.
            // Check if we have already performed the reversal or not.
            if (transaction.Transaction.Reversal.StatusCode == "00")
            {
                _logger.LogWarning($"ReverseFailedBankTransactions :: Transaction {transaction.ReferenceNumber} was already reversed.");
                return;
            }

            // Perform the reversal.
            var transferTerminalId = TransactionMerchantCodeService.GetMerchantCode(transaction.User.CardHolder.BelongsToExchangeHouse, transaction.User.ApplicationId, TransactionMerchantCodeFeature.MoneyTransfer);
            var transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.MoneyTransfer), transaction.MoneyTransferBeneficiary.FirstName);

            //Set the reversalAmount
            reversalAmount = reversalAmount != 0 ? reversalAmount : transaction.SendAmount;

            var transferFee = transaction.TotalCharges ?? 0;
            var totalAmount = reversalAmount + transferFee;

            var digits = transaction.ReferenceNumber.Where(c => Char.IsDigit(c));
            var reversedReferenceNumber = TransactionPrefix.REV + new string(digits.ToArray());

            var creditResult = new TopUpRequest()
            {
                Amount = decimal.Truncate(totalAmount * 100), // Reverse the amount + fee.
                CardPanNumber = transaction.Transaction.CardNumber,
                CardSerialNumber = transaction.Transaction.CardSerialNumber,

                Narration = transferNarration,
                Description = transferNarration,
                ReferenceNumber = reversedReferenceNumber,
                TerminalId = transferTerminalId,
                MerchantLoopCode = "5000",
            };

            _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Performing reversal for transaction: {transaction.ReferenceNumber}. Amount: AED {creditResult.Amount / 100}.");

            var tryDoReversal = await _ppsWebAuthService.DoTopUp(creditResult);

            if (tryDoReversal.IsSuccessful == false || tryDoReversal?.Data?.StatusCode != "00")
            {
                _logger.LogWarning($"BulkUpdateTransactionsStatuses :: Reversal failed for transaction: {transaction.ReferenceNumber}. Error: {tryDoReversal.ErrorMessage}");

                transaction.Status = Status.NEEDSMANUALREVERSAL;

                transaction.Transaction.Reversal = new TranscationReversal()
                {
                    StatusCode = tryDoReversal?.Data?.StatusCode,
                    StatusDescription = $"Reversal failed. Error: {tryDoReversal.ErrorMessage}"
                };

                // Add/update a failed or successful status step.
                var failedOrSuccessfulSteps = transaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED || s.Status == Status.SUCCESSFUL);
                foreach (var failedOrSuccessfulStep in failedOrSuccessfulSteps)
                {
                    failedOrSuccessfulStep.Hide = true;
                }

                transaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                {
                    Status = Status.FAILED,
                    Log = "Failed to reverse amount",
                    ProviderRemarks = $"Error: {tryDoReversal.ErrorMessage}",
                    Message = ConstantParam.UM_TransferFailed,
                });
            }
            else
            {
                _logger.LogDebug($"BulkUpdateTransactionsStatuses :: Reversal for transaction: {transaction.ReferenceNumber} was successful. Amount: AED {creditResult.Amount / 100}.");

                transaction.Status = Status.REVERSED;
                transaction.ReversalDate = DateTime.Now;

                transaction.Transaction.Reversal = new TranscationReversal()
                {
                    AuthenticationCode = tryDoReversal.Data.AuthCode,
                    Amount = TypeUtility.GetDecimalFromString(tryDoReversal.Data.Value.Amt),
                    EndBalance = TypeUtility.GetDecimalFromString(tryDoReversal.Data.EndBalanace.Amt),
                    Date = DateTime.Now,
                    ReferenceNumber = creditResult.ReferenceNumber,
                    StatusCode = tryDoReversal.Data.StatusCode,
                    StatusDescription = tryDoReversal.Data.Message
                };

                // Add/update a failed or successful status step.
                var failedOrSuccessfulSteps = transaction.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED || s.Status == Status.SUCCESSFUL);
                foreach (var failedOrSuccessfulStep in failedOrSuccessfulSteps)
                {
                    failedOrSuccessfulStep.Hide = true;
                }

                // Add a reversed step.
                // We need to show a correct message for the user based on why we reversed the money.
                var reversedStep = new MoneyTransferStatusStep()
                {
                    Status = Status.REVERSED,
                };

                switch (transaction.Remarks)
                {
                    case ConstantParam.PE_InvalidBeneficiaryAccountNumberError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_InvalidBeneficiaryAccountNumberError;
                        reversedStep.Message = ConstantParam.UM_InvalidBeneficiaryAccountNumber_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_InvalidBeneficiaryAccountNumberError}'";
                        break;
                    case ConstantParam.PE_AccountFrozenError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_AccountFrozenError;
                        reversedStep.Message = ConstantParam.UM_AccountFrozen_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_AccountFrozenError}'";
                        break;
                    case ConstantParam.PE_IncorrectPurposeCodeError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_IncorrectPurposeCodeError;
                        reversedStep.Message = ConstantParam.UM_IncorrectPurposeCode_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_IncorrectPurposeCodeError}'";
                        break;
                    case ConstantParam.PE_InvalidIfsCodeError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_InvalidIfsCodeError;
                        reversedStep.Message = ConstantParam.UM_InvalidIfsCode_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_InvalidIfsCodeError}'";
                        break;
                    case ConstantParam.PE_BeneficiaryAccountClosedError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_BeneficiaryAccountClosedError;
                        reversedStep.Message = ConstantParam.UM_BeneficiaryAccountClosed_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_BeneficiaryAccountClosedError}'";
                        break;
                    case ConstantParam.PE_InvalidBeneficiaryNameError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_InvalidBeneficiaryNameError;
                        reversedStep.Message = ConstantParam.UM_InvalidBeneficiaryName_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_InvalidBeneficiaryNameError}'";
                        break;
                    case ConstantParam.PE_BeneficiaryBankDidNotRespondError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_BeneficiaryBankDidNotRespondError;
                        reversedStep.Message = ConstantParam.UM_BeneficiaryBankDidNotRespond_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_BeneficiaryBankDidNotRespondError}'";
                        break;
                    case ConstantParam.PE_StopPaymentDoneError:
                        reversedStep.ProviderRemarks = ConstantParam.PE_StopPaymentDoneError;
                        reversedStep.Message = ConstantParam.UM_StopPaymentDone_R;
                        reversedStep.Log = $"Called RAK's GetMoneyTransferTransactionStatus and got '{ConstantParam.PE_StopPaymentDoneError}'";
                        break;
                    default:
                        reversedStep.ProviderRemarks = transaction.Remarks;
                        reversedStep.Message = ConstantParam.UM_TransferReversed_R;
                        reversedStep.Log = "Step set to reversed with no extra details.";
                        break;
                }

                transaction.MoneyTransferStatusSteps.Add(reversedStep);
            }

            await _unitOfWork.CommitAsync();

            if (transaction.Status == Status.REVERSED)
            {
                var profileId = transaction.User.CardHolderId;
                var beneficiary = transaction.MoneyTransferBeneficiary;
                var country = beneficiary.Country.Code3;
                var beneficiaryName = beneficiary.FirstName;

                var eventObject = new Tuple<string, MoneyTransferEvent, Status>(profileId, new MoneyTransferEvent()
                {
                    Amount = totalAmount,
                    BeneficiaryName = beneficiaryName,
                    Country = country,
                    ErrorMessage = null
                }, transaction.Status);

                var result = await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                {
                    eventObject
                });
            }
        }

        private async Task<string> CheckIfPartnerLimitIsReached(Guid userId, MoneyTransferType transferType, string countryCode, decimal transactionAmount)
        {
            var limitDetails = await this._unitOfWork.MoneyTransferTransactions.GetTransferLimitDetails(userId, transferType, countryCode, transactionAmount);

            if (limitDetails.LessThanMinAmount)
            {
                return TransferStatusValidationMessage.PartnerMinAmountReached.ToString();
            }
            else if (limitDetails.ExceedsAmount)
            {
                return TransferStatusValidationMessage.PartnerMaxAmountReached.ToString();
            }
            else if (limitDetails.ExceedsMonthlyAmount)
            {
                return BaseEnums.TransferStatusValidationMessage.MonthlyAmountLimitReached.ToString();
            }
            else if (limitDetails.ExceedsMonthlyCount)
            {
                return BaseEnums.TransferStatusValidationMessage.NoOfTransactionsLimitReached.ToString();
            }
            else
            {
                return string.Empty;
            }
        }

        private async Task<ServiceResponse<SendMoneyTransferResultDto>> SendDirectTransfer(MoneyTransferTransaction moneyTransferTransaction)
        {
            Func<Task> commitAction;
            if (generalSettings.IsDbSaveRetryEnabled)
                commitAction = async () => await this._unitOfWork.CommitWithRetryAsync();
            else
                commitAction = async () => await this._unitOfWork.CommitAsync();

            var beneficiary = await this._unitOfWork.MoneyTransferBeneficiaries.FirstOrDefaultAsync(
                b => !b.IsDeleted
                && b.Id == moneyTransferTransaction.MoneyTransferBeneficiaryId
                && b.TransferType == MoneyTransferType.DirectTransfer,
                u => u.User,
                u => u.User.CardHolder,
                u => u.LinkedUser);

            if (beneficiary is null)
            {
                return new ServiceResponse<SendMoneyTransferResultDto>(false, TransferStatusValidationMessage.BeneficiaryNotExists.ToString());
            }


            // Generate reference numbers.
            // DMT: Prefix
            // D: Debit, C: Credit
            // A: Amount, F: Fee

            // Examples: DMTDF123456789123
            // Examples: DMTDA123456789123
            // Examples: DMTCA123456789123

            var referenceNumbers = GetReferenceNumbers();

            var checkDone = false;

            while (checkDone != true)
            {
                var allReferenceNumbers = referenceNumbers.Select(r => r.ReferenceNumber).ToList();
                var foundDuplicates = await this._unitOfWork.Transactions.Any(t => allReferenceNumbers.Contains(t.ReferenceNumber));
                if (foundDuplicates == false)
                {
                    checkDone = true;
                }
                else
                {
                    referenceNumbers = GetReferenceNumbers();
                }
            }

            var debitFeeRef = referenceNumbers.First(r => r.Operation == TransactionPrefix.DMTD && r.DebitKind == DirectTransferDebitKind.Fee).ReferenceNumber;
            var debitAmountRef = referenceNumbers.First(r => r.Operation == TransactionPrefix.DMTD && r.DebitKind == DirectTransferDebitKind.Amount).ReferenceNumber;
            var creditAmountRef = referenceNumbers.First(r => r.Operation == TransactionPrefix.DMTC && r.DebitKind == DirectTransferDebitKind.Amount).ReferenceNumber;

            // Set properties.
            moneyTransferTransaction.UserId = beneficiary.UserId;
            moneyTransferTransaction.User = beneficiary.User;
            moneyTransferTransaction.MoneyTransferBeneficiary = beneficiary;
            moneyTransferTransaction.SendCurrency = ConstantParam.DefaultCurrency;
            moneyTransferTransaction.ReceiveCurrency = ConstantParam.DefaultCurrency;
            moneyTransferTransaction.ChargesCurrency = ConstantParam.DefaultCurrency;
            moneyTransferTransaction.ReceiveAmount = moneyTransferTransaction.SendAmount;
            moneyTransferTransaction.ChargesAmount = _moneyTransferServiceSettings.DirectTransferFee;
            moneyTransferTransaction.ChargeVat = _moneyTransferServiceSettings.DirectTransferVAT;
            moneyTransferTransaction.TotalCharges = moneyTransferTransaction.ChargesAmount + moneyTransferTransaction.ChargeVat;
            moneyTransferTransaction.ConversionRate = 1;
            moneyTransferTransaction.WaiveType = WaiveType.NotWaived;
            moneyTransferTransaction.Status = Status.PENDING;

            await this._unitOfWork.MoneyTransferTransactions.AddAsync(moneyTransferTransaction);

            var freeFeeDirectTransferBranches = _moneyTransferServiceSettings.FreeDirectTransferBranches.Split(",").Select(a => a.Trim()).ToList();
            if (freeFeeDirectTransferBranches.Contains(moneyTransferTransaction.User.CardHolder.CorporateId))
            {
                moneyTransferTransaction.WaiveType = WaiveType.CorporateExempted;
                moneyTransferTransaction.WaivedCharge = moneyTransferTransaction.TotalCharges;
                moneyTransferTransaction.TotalCharges = 0;
            }
            else
            {
                // Debit fee first.
                if (moneyTransferTransaction.TotalCharges > 0)
                {
                    var tryDebitFee = await this.DebitUser(moneyTransferTransaction, DirectTransferDebitKind.Fee, debitFeeRef);
                    if (tryDebitFee.IsSuccessful == false)
                    {
                        moneyTransferTransaction.Status = Status.FAILED;
                        moneyTransferTransaction.Remarks = "Failed to debit fee. Error: " + tryDebitFee.ErrorMessage;
                        await commitAction();

                        return new ServiceResponse<SendMoneyTransferResultDto>(false, tryDebitFee.ErrorMessage);
                    }

                    moneyTransferTransaction.ReferenceNumber = debitFeeRef;
                    moneyTransferTransaction.Remarks = ConstantParam.DebitedFeeInitiator;
                    await commitAction();
                }
            }

            // Debit amount.
            var tryDebitAmount = await this.DebitUser(moneyTransferTransaction, DirectTransferDebitKind.Amount, debitAmountRef, beneficiary.FirstName);
            if (tryDebitAmount.IsSuccessful == false)
            {
                if (tryDebitAmount.ErrorMessage == TransferStatusValidationMessage.ExceptionOccurred.ToString())
                {
                    moneyTransferTransaction.Status = Status.FAILED;
                }
                else
                {
                    moneyTransferTransaction.Status = moneyTransferTransaction.WaiveType == WaiveType.CorporateExempted ? Status.FAILED : Status.PENDINGFEEREVERSE;
                }

                moneyTransferTransaction.Remarks = "Failed to debit amount. Error: " + tryDebitAmount.ErrorMessage;
                await commitAction();

                return new ServiceResponse<SendMoneyTransferResultDto>(false, tryDebitAmount.ErrorMessage);
            }

            moneyTransferTransaction.ReferenceNumber = debitAmountRef;
            moneyTransferTransaction.Remarks = ConstantParam.DebitedAmountInitiator;
            await commitAction();

            if (beneficiary.LinkedUserId is null)
            {
                beneficiary.AccountNumber = beneficiary.AccountNumber.Trim();
                var linkedUser = await this._unitOfWork.Users.FirstOrDefaultAsync(
                    u => u.IsDeleted == false
                    && u.ApplicationId == MobileApplicationId.C3Pay
                    && u.PhoneNumber == beneficiary.AccountNumber, u => u.CardHolder);


                if (linkedUser != null)
                {
                    var allowedCorporates = await _partnerCorporateService.GetPartnerCorporates();
                    var userPartnerCode = _partnerCorporateService.GetUserPartnerCode(moneyTransferTransaction.User.CardHolder, allowedCorporates);
                    var linkedUserPartnerCode = _partnerCorporateService.GetUserPartnerCode(linkedUser.CardHolder, allowedCorporates);

                    if (userPartnerCode.Trim().ToLower() == linkedUserPartnerCode.Trim().ToLower())
                    {
                        beneficiary.LinkedUserId = linkedUser.Id;
                        beneficiary.Status = Status.APPROVED;
                        beneficiary.AccountNumber = null;

                        moneyTransferTransaction.MoneyTransferBeneficiary = beneficiary;
                        await commitAction();
                    }
                    else
                    {
                        beneficiary.IsCrossTransfer = true;
                        await commitAction();
                    }
                }
            }

            if (beneficiary.LinkedUserId != null)
            {
                var receiver = await this._unitOfWork.Users.FirstOrDefaultAsync(
                    u => !u.IsBlocked
                    && !u.IsDeleted
                    && u.Id == beneficiary.LinkedUserId,
                    u => u.CardHolder);

                if (receiver != null)
                {
                    var tryCreditAmount = await this.CreditUser(moneyTransferTransaction, receiver, creditAmountRef);
                    if (tryCreditAmount.IsSuccessful == false)
                    {
                        if (tryCreditAmount.ErrorMessage == TransferStatusValidationMessage.ExceptionOccurred.ToString())
                        {
                            moneyTransferTransaction.Status = Status.FAILED;
                        }
                        else
                        {
                            moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                        }

                        moneyTransferTransaction.Remarks = "Failed to credit amount. Error: " + tryCreditAmount.ErrorMessage;
                        await commitAction();

                        return new ServiceResponse<SendMoneyTransferResultDto>(false, tryCreditAmount.ErrorMessage);
                    }

                    moneyTransferTransaction.ReferenceNumber = creditAmountRef;
                    moneyTransferTransaction.Status = Status.SUCCESSFUL;
                    moneyTransferTransaction.Remarks = ConstantParam.DebitedInitiatorAndCreditedReceiver;

                    await commitAction();

                    // Send push notification to receiver.
                    if (string.IsNullOrEmpty(receiver.DeviceToken) == false)
                    {
                        await this._pushNotificationService.SendInstantDirectTransferPushNotification(receiver.DeviceToken, beneficiary.User.CardHolder.FirstName, moneyTransferTransaction.SendAmount);
                    }
                    else
                    {
                        _logger.LogWarning(ConstantParam.FailedToSendPushNotificationDueToNullToken, receiver.Id);
                    }
                }
            }
            else
            {
                // Send SMS to to the beneficiary mobile number.
                var sender = await this._unitOfWork.Users.FirstOrDefaultAsync(
                    u => u.IsDeleted == false
                    && u.Id == moneyTransferTransaction.UserId,
                    u => u.CardHolder);

                var trySendSms = await this._textMessageSenderService.SendPendingDirectTransferSMS(beneficiary.AccountNumber.ToShortPhoneNumber(), sender.CardHolder.FirstName, moneyTransferTransaction.SendAmount.ToString());
                if (!trySendSms.IsSuccessful)
                {
                    this._logger.LogError(ConstantParam.SendingPendingDirectTransferSMSFailed, beneficiary.AccountNumber.ToShortPhoneNumber(), trySendSms.ErrorMessage);
                }

            }

            var response = new SendMoneyTransferResultDto()
            {
                MoneyTransferTransaction = moneyTransferTransaction
            };
            return new ServiceResponse<SendMoneyTransferResultDto>(response);
        }

        private async Task<ServiceResponse<SendMoneyTransferResultDto>> SendInternationalTransfer(MoneyTransferTransaction moneyTransferTransaction)
        {
            var response = new SendMoneyTransferResultDto();

            var beneficiary = await this._unitOfWork.MoneyTransferBeneficiaries.FirstOrDefaultAsync(b => b.IsDeleted == false
                                                                                                         && b.Id == moneyTransferTransaction.MoneyTransferBeneficiaryId,
                                                                                                         i => i.User.CardHolder,
                                                                                                         i => i.ExternalBeneficiary,
                                                                                                         i => i.Country,
                                                                                                         i => i.User.UserSegmentation);



            if (beneficiary is null)
            {
                return new ServiceResponse<SendMoneyTransferResultDto>(false, TransferStatusValidationMessage.BeneficiaryNotExists.ToString());
            }

            // Find transfer method.
            var countryCode = beneficiary.CountryCode;
            MoneyTransferMethod transferMethod;

            if (beneficiary.TransferType == MoneyTransferType.OutsideUAE)
            {
                transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.MoneyTransferProvider.CountryCode == countryCode
                                                                                                      && x.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer,
                                                                                                      include => include.MoneyTransferProvider);
            }
            else if (beneficiary.TransferType == MoneyTransferType.RAKMoneyCashPayout)
            {
                transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.MoneyTransferProvider.CountryCode == countryCode
                                                                                                      && x.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.CashPickup,
                                                                                                      include => include.MoneyTransferProvider);
            }
            else if (beneficiary.TransferType == MoneyTransferType.Wallet)
            {
                transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.MoneyTransferProvider.CountryCode == countryCode
                                                                                                      && x.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.Wallet,
                                                                                                      include => include.MoneyTransferProvider);
            }
            else
            {
                transferMethod = null;
            }


            // Western Union stuff.
            if (transferMethod != null && transferMethod.MoneyTransferProvider.WUSendMoneyEnabled)
            {
                return await this.SendInstantInternationalTransfer(moneyTransferTransaction, transferMethod.MoneyTransferProvider.WUValidateTransferEnabled);
            }

            var user = beneficiary.User;
            #region Validate EID Expiry
            bool isEnabledMoneyTransferNonWUEIDGraceBlockTransaction = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferNonWUEIDGraceBlockTransaction);
            if (isEnabledMoneyTransferNonWUEIDGraceBlockTransaction)
            {
                int nonWUConfigGraceDays = _moneyTransferServiceSettings.NonWUEmiratesIdExpiryGracePeriodDays;
                if (user.CardHolder is null || (user.CardHolder.EmiratesIdExpiryDate.HasValue &&
                    DateTime.Now.Date >= Convert.ToDateTime(user.CardHolder.EmiratesIdExpiryDate).Date.AddDays(nonWUConfigGraceDays)))
                    return new ServiceResponse<SendMoneyTransferResultDto>(false, TransferStatusValidationMessage.EidExpired.ToString());
            }
            #endregion

            moneyTransferTransaction.ReceiveCurrency = beneficiary.Country.Currency;

            //Get the Fx Rate and Transfer fees
            var fxRateData = await GetFxRates(user.Id, moneyTransferTransaction.ReceiveCurrency, moneyTransferTransaction.SendAmount, ((TransferMethod)beneficiary.TransferType).ToString());
            if (fxRateData.IsSuccessful == false)
            {
                return new ServiceResponse<SendMoneyTransferResultDto>(false, fxRateData.ErrorMessage);
            }
            else
            {
                var fxRate = fxRateData.Data.FxRates.FirstOrDefault().FxConversionRates.FirstOrDefault();
                moneyTransferTransaction.ConversionRate = Convert.ToDecimal(fxRate.Rate);
                moneyTransferTransaction.ReceiveAmount = Convert.ToDecimal(fxRate.ConvertedAmount);
                moneyTransferTransaction.ChargesAmount = Convert.ToDecimal(fxRateData.Data.FxRates.FirstOrDefault().ConversionCharges.Amount);
                moneyTransferTransaction.TotalCharges = Convert.ToDecimal(fxRateData.Data.FxRates.FirstOrDefault().ConversionCharges.TotalCharges);
            }

            // No fee for the first transaction.
            if (fxRateData.Data.IsFirstTransfer)
            {
                moneyTransferTransaction.WaiveType = WaiveType.FirstTransaction;
            }
            // No fee for the transaction if the user has loyalty offer.  
            else if (fxRateData.Data.IsLoyalty)
            {
                moneyTransferTransaction.WaiveType = WaiveType.Loyalty;
            }

            if (fxRateData.Data.IsFirstTransfer || fxRateData.Data.IsLoyalty)
            {
                moneyTransferTransaction.WaivedCharge = moneyTransferTransaction.ChargesAmount;
                moneyTransferTransaction.TotalCharges = 0;
            }

            //Validation to check customer's balance
            var totalAmount = moneyTransferTransaction.SendAmount + moneyTransferTransaction.TotalCharges;
            var tryGetBalance = await this.GetBalance(user);
            if (!tryGetBalance.IsSuccessful || tryGetBalance.Data < totalAmount)
            {
                return new ServiceResponse<SendMoneyTransferResultDto>(false, TransferStatusValidationMessage.InsufficientBalance.ToString());
            }

            // Referral code.
            var referralCode = moneyTransferTransaction.ReferralCode;
            var userReferralCode = user.ReferralCode;

            if (referralCode == userReferralCode && !string.IsNullOrEmpty(userReferralCode))
            {
                moneyTransferTransaction.ReferralCode = null;
            }
            else
            {
                bool referralUserExists = false;

                if (!string.IsNullOrEmpty(referralCode))
                {
                    referralUserExists = await this._unitOfWork.Users.Any(u => u.Id != user.Id && u.ReferralCode == referralCode);
                }

                // Referred by user.
                if (referralUserExists)
                {
                    if (!fxRateData.Data.IsFirstTransfer || moneyTransferTransaction.SendAmount
                        < this._referralProgramServiceSettings.MoneyTransferAmountThreshold)
                    {
                        moneyTransferTransaction.ReferralCode = null;
                    }
                }
                // Referred by entering the code.
                else
                {
                    if (!fxRateData.Data.IsFirstTransfer)
                    {
                        var referrerCodeDetails = await this._unitOfWork.MoneyTransferTransactions.GetReferrerCodeDetails(
                            user.Id, moneyTransferTransaction.CreatedDate);
                        if (referrerCodeDetails.Eligible)
                        {
                            moneyTransferTransaction.ReferralCode = referrerCodeDetails.ReferrerCode;
                        }
                    }
                }
            }

            // Set transfer values.
            moneyTransferTransaction.UserId = beneficiary.User.Id;
            moneyTransferTransaction.TransferType = TransactionType.RAKMoney;
            moneyTransferTransaction.MoneyTransferReasonId = beneficiary.MoneyTransferReasonId;
            moneyTransferTransaction.Status = Status.PENDING;

            // Add an entry to money transfer status progress.
            moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();
            moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
            {
                Status = Status.STARTED,
                Message = ConstantParam.UM_TransferStarted,
                Log = "Status was set to started from SendMoney API.",
            });

            // Call started event.
            await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
             {
                 new Tuple<string, MoneyTransferEvent, Status>(user.CardHolderId, new MoneyTransferEvent()
                 {
                     Amount = moneyTransferTransaction.SendAmount,
                     BeneficiaryName = beneficiary.FirstName,
                     Country = beneficiary.Country.Code3,
                     ErrorMessage = ""
                 }, Status.STARTED)
             });

            await _unitOfWork.MoneyTransferTransactions.AddAsync(moneyTransferTransaction);
            await _unitOfWork.CommitAsync();

            await CreateSpinTheWheel(moneyTransferTransaction, user);

            try
            {
                if (beneficiary.TransferType == MoneyTransferType.RAKMoneyCashPayout)
                {
                    var externalServiceStatus = await this.AddExternalTransaction(moneyTransferTransaction.Id);

                    var processedTransaction = await _unitOfWork.MoneyTransferTransactions.FirstOrDefaultAsync(x => x.Id == moneyTransferTransaction.Id,
                        i => i.MoneyTransferBeneficiary,
                        i => i.MoneyTransferBeneficiary.ExternalBeneficiary,
                        i => i.MoneyTransferStatusSteps,
                        i => i.User,
                        i => i.User.CardHolder,
                        i => i.ExternalTransaction,
                        i => i.Transaction,
                        i => i.MoneyTransferBeneficiary.Country);

                    response.MoneyTransferTransaction = processedTransaction;

                    // message in queue for updating transaction status from RAK
                    if (externalServiceStatus.IsSuccessful && externalServiceStatus.Data == Status.COMPLETED)
                    {
                        await EnqueueTransactionStatusUpdateAsync(moneyTransferTransaction.Id, 1);
                    }

                }
                else
                {
                    //Put message in the queue            
                    await _messagingQueueService.SendAsync(new MoneyTransferMessageDto
                    {
                        Id = moneyTransferTransaction.Id,
                        Action = MessageAction.Create
                    },
                this._rakSettings.MoneyTransferQueueConnectionString,
                this._rakSettings.MoneyTransferQueueName, null);
                }

            }
            catch (Exception ex)
            {
                _logger.LogError($"Connectivity Issue :{moneyTransferTransaction.ReferenceNumber} && exception details {ex?.Message}");

                moneyTransferTransaction.Status = Status.FAILED;
                moneyTransferTransaction.Remarks = $"Connectivity Issue {ex?.Message}";

                // Add an entry to money transfer status progress.
                moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();
                moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                {
                    Status = Status.FAILED,
                    Message = ConstantParam.UM_TransferFailed,
                    Log = $"Transfer failed because of connectivity issues when trying to send the transfer details to the messaging queue service. Exception details {ex?.Message}.",
                });

                // Call failed event.
                await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                 {
                     new Tuple<string, MoneyTransferEvent, Status>(user.CardHolderId, new MoneyTransferEvent()
                     {
                         Amount = moneyTransferTransaction.SendAmount,
                         BeneficiaryName = beneficiary.FirstName,
                         Country = beneficiary.Country.Code3,
                         ErrorMessage = ex.Message
                     }, Status.FAILED)
                 });

                await _unitOfWork.CommitAsync();
            }

            // Transfer Delays.
            // There are some cases where money transfer could be potentially delayed.

            // We won't show the delay popup if the transfer has failed, so we have to check the status.
            if (moneyTransferTransaction.Status == Status.PENDING)
            {
                var country = await _unitOfWork.Countries.FirstOrDefaultAsync(z => z.Currency == moneyTransferTransaction.ReceiveCurrency.ToUpper());
                var dateNow = DateTime.Now;

                var delay = await this._unitOfWork.MoneyTransferDelays.FirstOrDefaultAsync(d => d.IsActive
                && d.CountryCode == country.Code
                && d.StartDate <= dateNow
                && d.EndDate > dateNow, i => i.Country);

                if (delay != null)
                {
                    // We have found a delay.
                    response.MoneyTransferDelay = delay;
                }
            }

            try
            {
                var userIsEligibleForSalaryAdvanceCashBackResult = await this._salaryAdvanceCashBackService.UserIsEligibleForCashback(user);

                if (userIsEligibleForSalaryAdvanceCashBackResult.IsSuccessful)
                {
                    var userIsEligible = userIsEligibleForSalaryAdvanceCashBackResult.Data.Item1;

                    var cashBackAmount = userIsEligibleForSalaryAdvanceCashBackResult.Data.Item2;

                    if (userIsEligible)
                        await this._salaryAdvanceCashBackService.CreditCashBack(user, cashBackAmount);
                }
                else
                    _logger.LogWarning($"Check Salary Advance Cashback Eligibility Failed. Error: {userIsEligibleForSalaryAdvanceCashBackResult.ErrorMessage}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while trying to add Salary Advance Cashback on {moneyTransferTransaction.ReferenceNumber} transaction with exception details {ex?.Message}");
            }

            if (beneficiary.TransferType != MoneyTransferType.RAKMoneyCashPayout)
            {
                response.MoneyTransferTransaction = moneyTransferTransaction;
            }

            // For MT Gold Incentive Acquisition
            var isAcquisitionGoldExperimentActive = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableGoldIncentiveAcquisition);
            if (isAcquisitionGoldExperimentActive)
            {
                try
                {
                    var isUserPartOfExperiment = await _unitOfWork.MoneyTransferUserRewards.Any(a => a.UserId == moneyTransferTransaction.UserId
                        && a.IsTransactionCompleted == false && a.IsDeleted == false);
                    if (isUserPartOfExperiment)
                        await _unitOfWork.MoneyTransferUserRewards.UpdateTransactionCompletedAsync(moneyTransferTransaction.UserId);
                }
                catch (Exception ex)
                {
                    _logger.LogError("Error in Updating MTAcquisitionExperiment" + ex.Message);
                }
            }
            return new ServiceResponse<SendMoneyTransferResultDto>(response);
        }

        private static List<DirectTransferReferenceNumber> GetReferenceNumbers()
        {
            var randomNumber = TypeUtility.GetReferenceNumber(string.Empty, 12);

            var referenceNumbers = new List<DirectTransferReferenceNumber>() {
                new DirectTransferReferenceNumber() // DMTDF123456789123
                {
                    Operation = TransactionPrefix.DMTD,
                    DebitKind = DirectTransferDebitKind.Fee,
                    RandomNumber = randomNumber,
                },
                 new DirectTransferReferenceNumber() // DMTDA123456789123
                {
                    Operation = TransactionPrefix.DMTD,
                    DebitKind = DirectTransferDebitKind.Amount,
                    RandomNumber = randomNumber,
                },
                  new DirectTransferReferenceNumber() // DMTCA123456789123
                {
                    Operation = TransactionPrefix.DMTC,
                    DebitKind = DirectTransferDebitKind.Amount,
                    RandomNumber = randomNumber,
                },
            };

            return referenceNumbers;
        }

        private async Task<ServiceResponse<decimal>> GetBalance(User user)
        {
            if (user.CardHolder == null)
            {
                return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.ErrorGettingBalance.ToString());
            }

            var cardNumber = user.CardHolder.CardNumber;
            var cardSerialNumber = user.CardHolder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var getCardBalanceResult = await _ppsWebAuthService.GetCardBalance(new BalanceRequest()
            {
                CardPanNumber = cardPanNumber,
                CardSerialNumber = cardSerialNumber,
                Narration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                Description = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                ReferenceNumber = TypeUtility.GetReferenceNumber(TransactionPrefix.BAL.ToString(), 12)
            });

            if (!getCardBalanceResult.IsSuccessful)
            {
                return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.PPSConnectionIssue.ToString());
            }

            if (getCardBalanceResult.Data.StatusCode != "00")
            {
                if (getCardBalanceResult.Data.Message == EnumUtility.GetDescriptionFromEnumValue(PPSResponseStatus.CARDNOTACTIVATED))
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.ActivateYourCard.ToString());
                }
                else if (getCardBalanceResult.Data.Message == EnumUtility.GetDescriptionFromEnumValue(PPSResponseStatus.MAXPINEXCEEDED))
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.UnblockYourCard.ToString());
                }
                else
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.ErrorGettingBalance.ToString());
                }
            }

            var balance = TypeUtility.GetDecimalFromString(getCardBalanceResult.Data.EndBalanace.Amt) / 100;
            return new ServiceResponse<decimal>(balance);
        }

        private async Task<ServiceResponse<bool>> DebitUser(MoneyTransferTransaction moneyTransferTransaction, DirectTransferDebitKind debitKind, string referenceNumber, string receiverName = null)
        {
            var cardNumber = moneyTransferTransaction.User.CardHolder.CardNumber;
            var cardSerialNumber = moneyTransferTransaction.User.CardHolder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var debitDetails = new RedemptionRequest()
            {
                CardSerialNumber = cardSerialNumber,
                CardPanNumber = cardPanNumber,
                MerchantLoopCode = "5000",
            };

            var application = moneyTransferTransaction.User.ApplicationId;
            var transferTerminalId = string.Empty;
            var transferNarration = string.Empty; ;
            var transferFeeTerminalId = string.Empty;
            var transferFeeNarration = string.Empty;
            var belongsToExchangeHouse = moneyTransferTransaction.User.CardHolder.BelongsToExchangeHouse;

            switch (application)
            {
                case MobileApplicationId.C3Pay:
                    transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayToC3PayTransferDebit), receiverName);
                    transferFeeNarration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayToC3PayTransferFee);
                    transferTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.C3Pay, TransactionMerchantCodeFeature.AppToAppTransfer);
                    transferFeeTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.C3Pay, TransactionMerchantCodeFeature.AppToAppTransferFee);

                    break;
                case MobileApplicationId.MySalary:
                    transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.MySalaryToMySalaryTransferDebit), receiverName);
                    transferFeeNarration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.MySalaryToMySalaryTransferFee);
                    transferTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.MySalary, TransactionMerchantCodeFeature.AppToAppTransfer);
                    transferFeeTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.MySalary, TransactionMerchantCodeFeature.AppToAppTransferFee);
                    break;
                default:
                    break;
            }

            switch (debitKind)
            {
                case DirectTransferDebitKind.Fee:
                    debitDetails.Amount = decimal.Truncate(moneyTransferTransaction.TotalCharges.Value * 100);
                    debitDetails.Narration = transferFeeNarration;
                    debitDetails.TerminalId = transferFeeTerminalId;
                    debitDetails.Description = transferFeeNarration;
                    debitDetails.ReferenceNumber = referenceNumber;

                    break;
                case DirectTransferDebitKind.Amount:
                    debitDetails.Amount = decimal.Truncate(moneyTransferTransaction.SendAmount * 100);
                    debitDetails.Narration = transferNarration;
                    debitDetails.TerminalId = transferTerminalId;
                    debitDetails.Description = transferNarration;
                    debitDetails.ReferenceNumber = referenceNumber;

                    break;
                default: throw new Exception("Debit kind not provided.");
            }

            // SIMULATION!!1!! DONT ADD THIS TO PROD PLZZZ :'(
            if (moneyTransferTransaction.SendAmount == 31 && debitKind == DirectTransferDebitKind.Amount)
            {
                _logger.LogError("Error DebitUser :: " + "Testing");
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.PPSConnectionIssue.ToString());
            }
            //END OF SIMULATION

            ServiceResponse<PPSWebAuthResponseModel> debitResult = new ServiceResponse<PPSWebAuthResponseModel>();
            try
            {
                debitResult = await _ppsWebAuthService.DoRedemption(debitDetails);
            }
            catch (Exception ex)
            {
                this._logger.LogError(ex, "Error debiting user due to PPS exception for reference number {referenceNumber}", referenceNumber);
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.ExceptionOccurred.ToString());
            }

            if (debitResult.IsSuccessful == false)
            {
                _logger.LogError("Error DebitUser :: " + debitResult.ErrorMessage);
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.PPSConnectionIssue.ToString());
            }

            if (debitResult.Data.StatusCode != "00")
            {
                _logger.LogError("Error DebitUser :: " + debitResult.Data.Message);
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.ErrorDebitingCard.ToString());
            }

            moneyTransferTransaction.Transaction = new Transaction()
            {
                BillPayType = EnumUtility.GetDescriptionFromEnumValue(PPSBillPayType.DMT),
                UserId = moneyTransferTransaction.UserId,
                PayeeId = debitResult.Data.PayeeId,
                ServiceProvider = CardPaymentServiceProvider.PPS,
                CardNumber = debitResult.Data.Account.No,
                CardSerialNumber = debitResult.Data.Account.Serial,
                CardAccountTerminalAddress = debitResult.Data.TerminalAddress,
                CardAccountTerminalId = debitResult.Data.TerminalId,
                Amount = TypeUtility.GetDecimalFromString(debitResult.Data.Value.Amt) / 100,
                StatusDescription = debitResult.Data.Message,
                Date = string.IsNullOrEmpty(debitResult.Data.TimeStamp.Val) ? string.Empty : debitResult.Data.TimeStamp.Val.ToString().Split(' ').First(),
                Time = string.IsNullOrEmpty(debitResult.Data.TimeStamp.Val) ? string.Empty : debitResult.Data.TimeStamp.Val.ToString().Split(' ').Last(),
                MacValue = debitResult.Data.HashValue,
                Origin = debitResult.Data.Origin,
                AuthenticationCode = debitResult.Data.AuthCode,
                ReferenceNumber = debitResult.Data.ReferenceId,
                EndBalance = TypeUtility.GetDecimalFromString(debitResult.Data.EndBalanace.Amt),
                CardAccountId = PPSAccountIdPrefix.RMT.GetHashCode().ToString() + PPSMerchantType.RMT.GetHashCode().ToString() + EnumUtility.GetDescriptionFromEnumValue(PPSBillPayType.RMT),
                StatusCode = Status.SUCCESSFUL.ToString()
            };

            return new ServiceResponse<bool>(true);
        }

        private async Task<ServiceResponse<bool>> CreditUser(MoneyTransferTransaction moneyTransferTransaction, User userToCredit, string referenceNumber)
        {
            // Replace reference number tag (from D to C).
            // D: Debit.
            // C: Credit.

            var cardNumber = userToCredit.CardHolder.CardNumber;
            var cardSerialNumber = userToCredit.CardHolder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            string referenceNumberDigits = new string(referenceNumber.Where(char.IsDigit).ToArray());
            referenceNumber = TransactionPrefix.DMTC.ToString() + ConstantParam.DirectTransferAmountPrefix + referenceNumberDigits;

            var application = moneyTransferTransaction.User.ApplicationId;

            var transferTerminalId = string.Empty;
            var transferNarration = string.Empty;
            var textFormatter = new CultureInfo("en-US", false).TextInfo;
            var senderName = textFormatter.ToTitleCase((moneyTransferTransaction.User.CardHolder.FirstName ?? "").ToLower()).Split(' ')[0];
            var belongsToExchangeHouse = userToCredit.CardHolder.BelongsToExchangeHouse;
            switch (application)
            {
                case MobileApplicationId.C3Pay:
                    transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayToC3PayTransferCredit), senderName);
                    transferTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.C3Pay, TransactionMerchantCodeFeature.AppToAppTransferCredit);
                    break;
                case MobileApplicationId.MySalary:
                    transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.MySalaryToMySalaryTransferCredit), senderName);
                    transferTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.MySalary, TransactionMerchantCodeFeature.AppToAppTransferCredit);
                    break;
                default:
                    break;
            }

            // SIMULATION!!1!! DONT ADD THIS TO PROD PLZZZ :'(
            if (moneyTransferTransaction.SendAmount == 33)
            {
                _logger.LogError("Error CreditUser :: " + "Testing");
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.PPSConnectionIssue.ToString());
            }
            //END OF SIMULATION

            ServiceResponse<PPSWebAuthResponseModel> creditResult = new ServiceResponse<PPSWebAuthResponseModel>();
            try
            {
                creditResult = await _ppsWebAuthService.DoTopUp(new TopUpRequest()
                {
                    Amount = decimal.Truncate(moneyTransferTransaction.SendAmount * 100),
                    CardSerialNumber = cardSerialNumber,
                    CardPanNumber = cardPanNumber,
                    Narration = transferNarration,
                    MerchantLoopCode = "5000",
                    TerminalId = transferTerminalId,
                    ReferenceNumber = referenceNumber,
                    Description = transferNarration
                });
            }
            catch (Exception ex)
            {
                this._logger.LogError(ex, "Error crediting user due to PPS exception for reference number {referenceNumber}", referenceNumber);
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.ExceptionOccurred.ToString());
            }

            if (!creditResult.IsSuccessful)
            {
                this._logger.LogError("Error CreditUser :: " + creditResult.ErrorMessage);
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.PPSConnectionIssue.ToString());
            }

            if (creditResult.Data.StatusCode != "00")
            {
                this._logger.LogError("Error CreditUser :: " + creditResult.Data.StatusCode + " :: " + creditResult.Data.Message);
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.ErrorCreditingCard.ToString());
            }

            moneyTransferTransaction.Transaction = new Transaction()
            {
                BillPayType = EnumUtility.GetDescriptionFromEnumValue(PPSBillPayType.DMT),
                UserId = userToCredit.Id,
                PayeeId = creditResult.Data.PayeeId,
                ServiceProvider = CardPaymentServiceProvider.PPS,
                CardNumber = creditResult.Data.Account.No,
                CardSerialNumber = creditResult.Data.Account.Serial,
                CardAccountTerminalAddress = creditResult.Data.TerminalAddress,
                CardAccountTerminalId = creditResult.Data.TerminalId,
                Amount = TypeUtility.GetDecimalFromString(creditResult.Data.Value.Amt) / 100,
                StatusDescription = creditResult.Data.Message,
                Date = string.IsNullOrEmpty(creditResult.Data.TimeStamp.Val) ? string.Empty : creditResult.Data.TimeStamp.Val.ToString().Split(' ').First(),
                Time = string.IsNullOrEmpty(creditResult.Data.TimeStamp.Val) ? string.Empty : creditResult.Data.TimeStamp.Val.ToString().Split(' ').Last(),
                MacValue = creditResult.Data.HashValue,
                Origin = creditResult.Data.Origin,
                AuthenticationCode = creditResult.Data.AuthCode,
                ReferenceNumber = creditResult.Data.ReferenceId,
                EndBalance = TypeUtility.GetDecimalFromString(creditResult.Data.EndBalanace.Amt),
                CardAccountId = PPSAccountIdPrefix.RMT.GetHashCode().ToString() + PPSMerchantType.RMT.GetHashCode().ToString() + EnumUtility.GetDescriptionFromEnumValue(PPSBillPayType.RMT),
                StatusCode = Status.SUCCESSFUL.ToString()
            };

            return new ServiceResponse<bool>(true);
        }

        public async Task<ServiceResponse<bool>> CancelTransfer(User user, Guid transactionId)
        {
            Func<Task> commitAction;
            if (generalSettings.IsDbSaveRetryEnabled)
                commitAction = async () => await this._unitOfWork.CommitWithRetryAsync();
            else
                commitAction = async () => await this._unitOfWork.CommitAsync();

            var moneyTransferTransaction = await this._unitOfWork.MoneyTransferTransactions.FirstOrDefaultAsync(
                m => !m.IsDeleted
                && m.UserId == user.Id
                && m.Id == transactionId,
                m => m.MoneyTransferBeneficiary,
                m => m.Transaction,
                m => m.User);

            if (moneyTransferTransaction is null)
            {
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.TransactionNotExists.ToString());
            }

            if (moneyTransferTransaction.MoneyTransferBeneficiary.TransferType != MoneyTransferType.DirectTransfer
                || moneyTransferTransaction.Status != Status.PENDING)
            {
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.CantBeCanceledOrHasBeenClaimed.ToString());
            }

            string thisTransactionNumber = moneyTransferTransaction.ReferenceNumber;
            bool isFeeSucceeded = false, isDebitSucceeded = false, isCreditSucceeded = false;
            var statusResult = await CheckTransactionStatus(moneyTransferTransaction.ReferenceNumber);
            isFeeSucceeded = statusResult.Item1;
            isDebitSucceeded = statusResult.Item2;
            isCreditSucceeded = statusResult.Item3;

            // Credit Successful
            if (isDebitSucceeded && isCreditSucceeded)
            {
                moneyTransferTransaction.Status = Status.SUCCESSFUL;
                string creditReferenceNumber = $"{TransactionPrefix.DMTC}{EnumUtility.GetDescriptionFromEnumValue(DirectTransferDebitKind.Amount)}{new string(thisTransactionNumber.Where(char.IsDigit).ToArray())}";
                if (moneyTransferTransaction.ReferenceNumber != creditReferenceNumber)
                {
                    moneyTransferTransaction.Transaction = new Transaction()
                    {
                        ReferenceNumber = creditReferenceNumber,
                        ServiceProvider = CardPaymentServiceProvider.PPS,
                        BillPayType = EnumUtility.GetDescriptionFromEnumValue(PPSBillPayType.DMT),
                        UserId = moneyTransferTransaction.UserId,
                        Date = string.Empty,
                        Time = string.Empty,
                        StatusCode = "SUCCESSFUL",
                        StatusDescription = string.Empty,
                        CreatedDate = DateTime.Now,
                        IsDeleted = false,
                        Amount = moneyTransferTransaction.ReceiveAmount
                    };
                }
                await commitAction();
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.CantBeCanceledOrHasBeenClaimed.ToString());
            }

            if (isDebitSucceeded && !isCreditSucceeded)
                thisTransactionNumber = "DMTDA" + new string(moneyTransferTransaction.ReferenceNumber.Where(char.IsDigit).ToArray());
            else if (!isDebitSucceeded)
            {
                this._logger.LogError($"isDebitTransaction_No_{moneyTransferTransaction.Id.ToString()}");
                moneyTransferTransaction.Status = Status.FAILED;
                await commitAction();
                return new ServiceResponse<bool>(true);
            }

            var application = moneyTransferTransaction.User.ApplicationId;
            var transferTerminalId = string.Empty;
            var transferNarration = string.Empty;
            var receiverName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName;
            var belongsToExchangeHouse = user.CardHolder.BelongsToExchangeHouse;

            switch (application)
            {
                case MobileApplicationId.C3Pay:
                    transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayToC3PayTransferCancellation), receiverName);
                    transferTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.C3Pay, TransactionMerchantCodeFeature.AppToAppTransfer);

                    break;
                case MobileApplicationId.MySalary:
                    transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.MySalaryToMySalaryTransferCancellation), receiverName);
                    transferTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.MySalary, TransactionMerchantCodeFeature.AppToAppTransfer);

                    break;
                default:
                    break;
            }

            var reversalRequest = new RedemptionReversalRequest()
            {
                Amount = decimal.Truncate(moneyTransferTransaction.SendAmount * 100),
                CardPanNumber = moneyTransferTransaction.Transaction.CardNumber,
                CardSerialNumber = moneyTransferTransaction.Transaction.CardSerialNumber,
                RedemptionId = thisTransactionNumber,
                Narration = transferNarration,
                Description = transferNarration,
                ReferenceNumber = thisTransactionNumber,
                TerminalId = transferTerminalId,
                MerchantLoopCode = "5000",
            };

            var redemptionReversalResult = await _ppsWebAuthService.DoRedemptionReversal(reversalRequest);

            if (redemptionReversalResult.IsSuccessful == false || redemptionReversalResult.Data.StatusCode != "00")
            {
                this._logger.LogError(ConstantParam.FailedToReverseStatementTransaction, moneyTransferTransaction.UserId, decimal.Truncate(moneyTransferTransaction.SendAmount * 100));
                this._logger.LogError(redemptionReversalResult.ErrorMessage);

                moneyTransferTransaction.Transaction.Reversal.StatusCode = Status.FAILED.ToString();
                moneyTransferTransaction.Transaction.Reversal.StatusDescription = $"Reversal failed. Error: {redemptionReversalResult.ErrorMessage}";

            }
            else
            {
                this._logger.LogInformation(ConstantParam.StatementTransactionReversed, moneyTransferTransaction.UserId, decimal.Truncate(moneyTransferTransaction.SendAmount * 100));

                moneyTransferTransaction.Status = Status.CANCELED;
                moneyTransferTransaction.Transaction.AuthenticationCode = redemptionReversalResult.Data.AuthCode;
                moneyTransferTransaction.Transaction.Reversal.Amount = TypeUtility.GetDecimalFromString(redemptionReversalResult.Data.Value.Amt);
                moneyTransferTransaction.Transaction.Reversal.EndBalance = TypeUtility.GetDecimalFromString(redemptionReversalResult.Data.EndBalanace.Amt);
                moneyTransferTransaction.Transaction.Reversal.Date = DateTime.Now;
                moneyTransferTransaction.Transaction.Reversal.ReferenceNumber = reversalRequest.ReferenceNumber;
                moneyTransferTransaction.Transaction.Reversal.StatusCode = redemptionReversalResult.Data.StatusCode;
                moneyTransferTransaction.Transaction.Reversal.StatusDescription = redemptionReversalResult.Data.Message;
            }

            await commitAction();
            return new ServiceResponse<bool>(true);
        }

        public async Task<ServiceResponse<MoneyTransferRateComparison>> GetRateComparisonDetails(Guid userId, string nationality)
        {
            var c3PayDetails = new MoneyTransferComparisonDetails();
            var exchangeHouseDetails = new MoneyTransferComparisonDetails();

            c3PayDetails.ReceiveAmount = _moneyTransferServiceSettings.ComparisonReceiveAmount;
            exchangeHouseDetails.ReceiveAmount = _moneyTransferServiceSettings.ComparisonReceiveAmount;

            var country = await _unitOfWork.Countries.FirstOrDefaultAsync(x => x.Code3 == nationality);
            if (country != null)
            {
                c3PayDetails.CountryName = country.Name;
                c3PayDetails.CountryCurrency = country.Currency;
                c3PayDetails.Rate = 1 / country.BankTransferLatestRate;
                c3PayDetails.SendAmount = c3PayDetails.CalculateSendAmount();

                exchangeHouseDetails.CountryName = country.Name;
                exchangeHouseDetails.CountryCurrency = country.Currency;
                exchangeHouseDetails.Rate = c3PayDetails.Rate + _moneyTransferServiceSettings.ComparisonEHRateIncrement;
                exchangeHouseDetails.SendAmount = exchangeHouseDetails.CalculateSendAmount();
                exchangeHouseDetails.TransferFee = _moneyTransferServiceSettings.ComparisonEHTransferFee;

                var c3PayTransferFees = await _lookupService.GetTransferFees(country.Code,
                                                                             TransferMethod.BANKTRANSFER,
                                                                             c3PayDetails.SendAmount,
                                                                             userId);
                if (c3PayTransferFees.Data.HasValue)
                {
                    //Set the transfer fees exclusive of VAT and rounding to two decimals
                    c3PayDetails.TransferFee = Math.Round(c3PayTransferFees.Data.Value * 100 / 105, 2);
                }

                c3PayDetails.Total = c3PayDetails.CalculateTotal();
                exchangeHouseDetails.Total = exchangeHouseDetails.CalculateTotal();

                //Rounding the rates to two decimals
                c3PayDetails.Rate = Math.Round(c3PayDetails.Rate, 2);
                exchangeHouseDetails.Rate = Math.Round(exchangeHouseDetails.Rate, 2);

                return new ServiceResponse<MoneyTransferRateComparison>(new MoneyTransferRateComparison
                {
                    C3PayDetails = c3PayDetails,
                    ExchangeHouseDetails = exchangeHouseDetails
                });
            }
            else
            {
                return new ServiceResponse<MoneyTransferRateComparison>(false, "Unable to fetch comparison data");
            }
        }

        public async Task<ServiceResponse<bool>> ClaimDirectMoneyTransfers(string phoneNumber)
        {
            var moneyTransferTransactions = await this._unitOfWork.MoneyTransferTransactions.FindAsync(
                m => m.IsDeleted == false
                && m.MoneyTransferBeneficiary.LinkedUser.PhoneNumber == phoneNumber
                && m.TransferType == TransactionType.Direct
                && m.Status == Status.PENDING,
                false,
                m => m.User.CardHolder,
                m => m.MoneyTransferBeneficiary.LinkedUser.CardHolder);

            this._logger.LogInformation(ConstantParam.FoundNTransfersToClaim, moneyTransferTransactions.Count, phoneNumber);

            if (moneyTransferTransactions is null || moneyTransferTransactions.Count == 0)
            {
                this._logger.LogError(TransferStatusValidationMessage.TransactionNotExists.ToString());
                return new ServiceResponse<bool>(false, TransferStatusValidationMessage.TransactionNotExists.ToString());
            }

            foreach (var moneyTransferTransaction in moneyTransferTransactions)
            {

                if (moneyTransferTransaction is null || moneyTransferTransaction.MoneyTransferBeneficiary is null)
                {
                    continue;
                }

                var receiver = moneyTransferTransaction.MoneyTransferBeneficiary.LinkedUser;

                if (receiver.IsBlocked)
                {
                    this._logger.LogWarning(TransferStatusValidationMessage.UserBlocked.ToString());
                    continue;
                }

                var tryCreditAmount = await this.CreditUser(moneyTransferTransaction, receiver, moneyTransferTransaction.ReferenceNumber);
                if (tryCreditAmount.IsSuccessful == false)
                {
                    if (tryCreditAmount.ErrorMessage == TransferStatusValidationMessage.ExceptionOccurred.ToString())
                    {
                        moneyTransferTransaction.Status = Status.FAILED;
                    }
                    else
                    {
                        moneyTransferTransaction.Status = Status.PENDINGREVERSE;
                    }

                    moneyTransferTransaction.Remarks = "Failed to credit amount to claimed receiver. Error: " + tryCreditAmount.ErrorMessage;
                    await this._unitOfWork.CommitAsync();

                    return new ServiceResponse<bool>(false, tryCreditAmount.ErrorMessage);
                }
                moneyTransferTransaction.Status = Status.SUCCESSFUL;
                moneyTransferTransaction.Remarks = ConstantParam.DirectTransferClaimed;

                await this._unitOfWork.CommitAsync();

                this._logger.LogInformation(ConstantParam.DirectTransferClaimed);

                // Send push notifications to both sender and receiver.
                var sender = moneyTransferTransaction.User;

                if (string.IsNullOrEmpty(sender.DeviceToken) == false)
                {
                    await this._pushNotificationService.SendClaimedDirectTransferToSenderPushNotification(sender.DeviceToken,
                                                                                                          receiver.CardHolder.FirstName,
                                                                                                          moneyTransferTransaction.SendAmount);
                }
                else
                {
                    _logger.LogWarning(ConstantParam.FailedToSendPushNotificationDueToNullToken, sender.Id);
                }

                if (string.IsNullOrEmpty(receiver.DeviceToken) == false)
                {
                    await this._pushNotificationService.SendClaimedDirectTransferToReceiverPushNotification(receiver.DeviceToken,
                                                                                                            sender.CardHolder.FirstName,
                                                                                                            moneyTransferTransaction.SendAmount);
                }
                else
                {
                    _logger.LogWarning(ConstantParam.FailedToSendPushNotificationDueToNullToken, receiver.Id);
                }

                // Send SMS to receiver.
                await this._textMessageSenderService.SendClaimedDirectTransferToReceiverSMS(receiver.PhoneNumber.ToShortPhoneNumber(),
                                                                                            sender.CardHolder.FirstName,
                                                                                            moneyTransferTransaction.SendAmount);

            }

            return new ServiceResponse<bool>(true);
        }

        public async Task<ServiceResponse> ReversePendingDirectMoneyTransfers()
        {
            Func<Task> commitAction;
            if (generalSettings.IsDbSaveRetryEnabled)
                commitAction = async () => await this._unitOfWork.CommitWithRetryAsync();
            else
                commitAction = async () => await this._unitOfWork.CommitAsync();


            // Get all pending transfers.

            var debitAmountReference = TransactionPrefix.DMTD.ToString() + EnumUtility.GetDescriptionFromEnumValue(DirectTransferDebitKind.Amount);

            var moneyTransferTransactions = await this._unitOfWork.MoneyTransferTransactions.FindAsync(
                          m => m.IsDeleted == false
                          && m.CreatedDate < DateTime.Now.AddMinutes(-this._moneyTransferServiceSettings.ReversePendingDirectMoneyTransfersDurationInMin)
                          && m.TransferType == TransactionType.Direct
                          && m.ReferenceNumber.Contains(debitAmountReference)
                          && m.Status == Status.PENDING,
                          false,
                          m => m.MoneyTransferBeneficiary,
                          m => m.Transaction,
                          m => m.User,
                          m => m.User.CardHolder);


            foreach (var transaction in moneyTransferTransactions)
            {
                bool proceedReversal = false;
                // Check Credit transaction status for entry with Pending Status
                string thisTransactionNumber = transaction.ReferenceNumber;
                bool isFeeSucceeded = false, isDebitSucceeded = false, isCreditSucceeded = false;
                var statusResult = await CheckTransactionStatus(transaction.ReferenceNumber);
                isFeeSucceeded = statusResult.Item1;
                isDebitSucceeded = statusResult.Item2;
                isCreditSucceeded = statusResult.Item3;

                // Credit Successful
                if (isCreditSucceeded)
                {
                    transaction.Status = Status.SUCCESSFUL;
                    string creditReferenceNumber = $"{TransactionPrefix.DMTC}{EnumUtility.GetDescriptionFromEnumValue(DirectTransferDebitKind.Amount)}{new string(thisTransactionNumber.Where(char.IsDigit).ToArray())}";
                    if (transaction.ReferenceNumber != creditReferenceNumber)
                    {
                        transaction.Transaction = new Transaction()
                        {
                            ReferenceNumber = creditReferenceNumber,
                            ServiceProvider = CardPaymentServiceProvider.PPS,
                            BillPayType = EnumUtility.GetDescriptionFromEnumValue(PPSBillPayType.DMT),
                            UserId = transaction.UserId,
                            Date = string.Empty,
                            Time = string.Empty,
                            StatusCode = "SUCCESSFUL",
                            StatusDescription = string.Empty,
                            CreatedDate = DateTime.Now,
                            IsDeleted = false,
                            Amount = transaction.ReceiveAmount
                        };
                    }
                    await commitAction();
                }

                if (isDebitSucceeded && !isCreditSucceeded)
                {
                    thisTransactionNumber = "DMTDA" + new string(transaction.ReferenceNumber.Where(char.IsDigit).ToArray());
                    proceedReversal = true;
                }
                else if (!isDebitSucceeded)
                {
                    this._logger.LogError($"isDebitTransaction_No_{transaction.Id.ToString()}");
                    transaction.Status = Status.FAILED;
                    await commitAction();
                }

                if (proceedReversal == true)
                {

                    var application = transaction.User.ApplicationId;

                    var transferTerminalId = string.Empty;
                    var transferNarration = string.Empty;
                    var receiverName = transaction.MoneyTransferBeneficiary.FirstName;
                    var belongsToExchangeHouse = transaction.User.CardHolder.BelongsToExchangeHouse;

                    switch (application)
                    {
                        case MobileApplicationId.C3Pay:
                            transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayToC3PayTransferCancellation), receiverName);
                            transferTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.C3Pay, TransactionMerchantCodeFeature.AppToAppTransfer);

                            break;
                        case MobileApplicationId.MySalary:
                            transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.MySalaryToMySalaryTransferCancellation), receiverName);
                            transferTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.MySalary, TransactionMerchantCodeFeature.AppToAppTransfer);

                            break;
                        default:
                            break;
                    }

                    try
                    {
                        var reversalRequest = new RedemptionReversalRequest()
                        {
                            Amount = decimal.Truncate(transaction.SendAmount * 100),
                            CardPanNumber = transaction.Transaction.CardNumber,
                            CardSerialNumber = transaction.Transaction.CardSerialNumber,
                            RedemptionId = thisTransactionNumber,
                            Narration = transferNarration,
                            Description = transferNarration,
                            ReferenceNumber = thisTransactionNumber,
                            TerminalId = transferTerminalId,
                            MerchantLoopCode = "5000",
                        };

                        var redemptionReversalResult = await _ppsWebAuthService.DoRedemptionReversal(reversalRequest);

                        if (!redemptionReversalResult.IsSuccessful || redemptionReversalResult.Data.Value == null)
                        {
                            this._logger.LogError(ConstantParam.FailedToReverseStatementTransaction, transaction.UserId, decimal.Truncate(transaction.SendAmount));
                            this._logger.LogError($"Error: {redemptionReversalResult.ErrorMessage}");

                            transaction.Status = Status.NEEDSMANUALREVERSAL;

                            transaction.Transaction.Reversal = new TranscationReversal();
                            transaction.Transaction.Reversal.StatusCode = Status.FAILED.ToString();
                            transaction.Transaction.Reversal.StatusDescription = "Reversal failed.";
                        }
                        else
                        {

                            transaction.Status = Status.AUTOCANCELED;
                            transaction.Remarks = ConstantParam.AutoCancelled;
                            transaction.ReversalDate = DateTime.Now;

                            transaction.Transaction.Reversal = new TranscationReversal();
                            transaction.Transaction.Reversal.AuthenticationCode = redemptionReversalResult.Data.AuthCode;
                            transaction.Transaction.Reversal.Amount = TypeUtility.GetDecimalFromString(redemptionReversalResult.Data.Value.Amt);
                            transaction.Transaction.Reversal.EndBalance = TypeUtility.GetDecimalFromString(redemptionReversalResult.Data.EndBalanace.Amt);
                            transaction.Transaction.Reversal.Date = DateTime.Now;
                            transaction.Transaction.Reversal.ReferenceNumber = reversalRequest.ReferenceNumber;
                            transaction.Transaction.Reversal.StatusCode = redemptionReversalResult.Data.StatusCode;
                            transaction.Transaction.Reversal.StatusDescription = redemptionReversalResult.Data.Message;
                        }
                        await commitAction();
                    }
                    catch (Exception ex)
                    {
                        this._logger.LogError($"Error: {ex.Message}");
                        continue;
                    }
                }
            }
            await commitAction();
            return new ServiceResponse();
        }

        private async Task<(bool, bool, bool)> CheckTransactionStatus(string referenceNumber)
        {
            bool isFeeRefNo = false, isDebitRefNo = false, isCreditRefNo = false;
            isFeeRefNo = referenceNumber.Contains($"{TransactionPrefix.DMTD}{EnumUtility.GetDescriptionFromEnumValue(DirectTransferDebitKind.Fee)}");
            isDebitRefNo = referenceNumber.Contains($"{TransactionPrefix.DMTD}{EnumUtility.GetDescriptionFromEnumValue(DirectTransferDebitKind.Amount)}");
            isCreditRefNo = referenceNumber.Contains($"{TransactionPrefix.DMTC}{EnumUtility.GetDescriptionFromEnumValue(DirectTransferDebitKind.Amount)}");

            if (isCreditRefNo)
                return (true, true, true);
            if (isDebitRefNo || isFeeRefNo)
            {
                bool checkDebit = isFeeRefNo, checkCredit = isDebitRefNo;

                // Check for Credit
                string transactionToCheck = string.Empty;
                if (checkDebit)
                    transactionToCheck = $"{TransactionPrefix.DMTD}{EnumUtility.GetDescriptionFromEnumValue(DirectTransferDebitKind.Amount)}" +
                    new string(referenceNumber.Where(char.IsDigit).ToArray());
                if (checkCredit)
                    transactionToCheck = $"{TransactionPrefix.DMTC}{EnumUtility.GetDescriptionFromEnumValue(DirectTransferDebitKind.Amount)}" +
                    new string(referenceNumber.Where(char.IsDigit).ToArray());
                try
                {
                    var statusResponse = await this._transactionsB2CService.GetTransactionStatusBySerialNumber(transactionToCheck);
                    if (statusResponse.IsSuccessful && statusResponse.Data != null)
                    {
                        if (statusResponse.Data.isCompletedTransaction)
                        {
                            if (checkDebit)
                            {
                                isDebitRefNo = true;
                                isCreditRefNo = false;
                            }
                            if (checkCredit)
                            {
                                isCreditRefNo = true;
                                isDebitRefNo = true;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    this._logger.LogWarning($"CheckTransactionStatus_Orion_Failed_For_{transactionToCheck}");
                }
            }
            return (isFeeRefNo, isDebitRefNo, isCreditRefNo);
        }

        public async Task<ServiceResponse> ReverseFailedDirectMoneyTransfers()
        {
            // Get all pending direct transfers.

            var debitAmountReference = TransactionPrefix.DMTD.ToString() + EnumUtility.GetDescriptionFromEnumValue(DirectTransferDebitKind.Amount);
            var debitFeeReference = TransactionPrefix.DMTD.ToString() + EnumUtility.GetDescriptionFromEnumValue(DirectTransferDebitKind.Fee);

            var moneyTransferTransactions = await this._unitOfWork.MoneyTransferTransactions.FindAsync(
                          m => m.IsDeleted == false
                          && m.TransferType == TransactionType.Direct
                          && m.ReferenceNumber != null
                          && (m.Status == Status.PENDINGREVERSE || m.Status == Status.PENDINGFEEREVERSE),
                          false,
                          m => m.MoneyTransferBeneficiary,
                          m => m.Transaction,
                          m => m.User,
                          m => m.User.CardHolder);


            foreach (var moneyTransferTransaction in moneyTransferTransactions)
            {

                var application = moneyTransferTransaction.User.ApplicationId;

                var transferTerminalId = string.Empty;
                var transferNarration = string.Empty;
                var receiverName = moneyTransferTransaction.MoneyTransferBeneficiary.FirstName;
                var belongsToExchangeHouse = moneyTransferTransaction.User.CardHolder.BelongsToExchangeHouse;

                switch (application)
                {
                    case MobileApplicationId.C3Pay:
                        transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayToC3PayTransferDebit), receiverName) + " Reversal";
                        transferTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.C3Pay, TransactionMerchantCodeFeature.AppToAppTransfer);

                        break;
                    case MobileApplicationId.MySalary:
                        transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.MySalaryToMySalaryTransferDebit), receiverName) + " Reversal";
                        transferTerminalId = TransactionMerchantCodeService.GetMerchantCode(belongsToExchangeHouse, MobileApplicationId.MySalary, TransactionMerchantCodeFeature.AppToAppTransfer);

                        break;
                    default:
                        break;
                }

                if (moneyTransferTransaction.Status == Status.PENDINGREVERSE)
                {
                    try
                    {
                        var reversalRequest = new RedemptionReversalRequest()
                        {
                            Amount = decimal.Truncate(moneyTransferTransaction.SendAmount * 100),
                            CardPanNumber = moneyTransferTransaction.Transaction.CardNumber,
                            CardSerialNumber = moneyTransferTransaction.Transaction.CardSerialNumber,
                            RedemptionId = moneyTransferTransaction.ReferenceNumber,
                            Narration = transferNarration,
                            Description = transferNarration,
                            ReferenceNumber = moneyTransferTransaction.ReferenceNumber,
                            TerminalId = transferTerminalId,
                            MerchantLoopCode = "5000",
                        };

                        var redemptionReversalResult = await _ppsWebAuthService.DoRedemptionReversal(reversalRequest);
                        if (!redemptionReversalResult.IsSuccessful || redemptionReversalResult.Data.Value == null)
                        {
                            this._logger.LogError(ConstantParam.FailedToReverseStatementTransaction, moneyTransferTransaction.UserId, decimal.Truncate(moneyTransferTransaction.SendAmount));
                            this._logger.LogError($"Error: {redemptionReversalResult.ErrorMessage}");

                            moneyTransferTransaction.Status = Status.NEEDSMANUALREVERSAL;

                            moneyTransferTransaction.Transaction.Reversal = new TranscationReversal();
                            moneyTransferTransaction.Transaction.Reversal.StatusCode = Status.FAILED.ToString();
                            moneyTransferTransaction.Transaction.Reversal.StatusDescription = "Reversal failed.";
                        }
                        else
                        {

                            moneyTransferTransaction.Status = Status.PENDINGFEEREVERSE;
                            moneyTransferTransaction.ReversalDate = DateTime.Now;

                            moneyTransferTransaction.Transaction.Reversal = new TranscationReversal();
                            moneyTransferTransaction.Transaction.Reversal.AuthenticationCode = redemptionReversalResult.Data.AuthCode;
                            moneyTransferTransaction.Transaction.Reversal.Amount = TypeUtility.GetDecimalFromString(redemptionReversalResult.Data.Value.Amt);
                            moneyTransferTransaction.Transaction.Reversal.EndBalance = TypeUtility.GetDecimalFromString(redemptionReversalResult.Data.EndBalanace.Amt);
                            moneyTransferTransaction.Transaction.Reversal.Date = DateTime.Now;
                            moneyTransferTransaction.Transaction.Reversal.ReferenceNumber = reversalRequest.ReferenceNumber;
                            moneyTransferTransaction.Transaction.Reversal.StatusCode = redemptionReversalResult.Data.StatusCode;
                            moneyTransferTransaction.Transaction.Reversal.StatusDescription = redemptionReversalResult.Data.Message;
                        }

                        await this._unitOfWork.CommitAsync();
                    }
                    catch (Exception ex)
                    {
                        this._logger.LogError($"Error: {ex.Message}");
                        continue;
                    }
                }

                //Reversal of fee
                if (moneyTransferTransaction.Status == Status.PENDINGFEEREVERSE && moneyTransferTransaction.TotalCharges > 0)
                {
                    var tempFeeReferenceNumber = moneyTransferTransaction.ReferenceNumber.Replace(debitAmountReference, debitFeeReference);
                    var transaction = await _unitOfWork.Transactions.FirstOrDefaultAsync(x => x.ReferenceNumber == tempFeeReferenceNumber);
                    if (transaction != null && transaction.Reversal.StatusCode != "00")
                    {

                        switch (application)
                        {
                            case MobileApplicationId.C3Pay:
                                transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayToC3PayTransferFee), receiverName) + " Reversal";
                                break;
                            case MobileApplicationId.MySalary:
                                transferNarration = string.Format(EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.MySalaryToMySalaryTransferFee), receiverName) + " Reversal";
                                break;
                            default:
                                break;
                        }

                        try
                        {
                            var reversalRequest = new RedemptionReversalRequest()
                            {
                                Amount = decimal.Truncate(moneyTransferTransaction.TotalCharges.Value * 100),
                                CardPanNumber = transaction.CardNumber,
                                CardSerialNumber = transaction.CardSerialNumber,
                                RedemptionId = tempFeeReferenceNumber,
                                Narration = transferNarration,
                                Description = transferNarration,
                                ReferenceNumber = tempFeeReferenceNumber,
                                TerminalId = transferTerminalId,
                                MerchantLoopCode = "5000",
                            };

                            var redemptionReversalResult = await _ppsWebAuthService.DoRedemptionReversal(reversalRequest);
                            if (!redemptionReversalResult.IsSuccessful || redemptionReversalResult.Data.Value == null)
                            {
                                this._logger.LogError(ConstantParam.FailedToReverseStatementTransaction, moneyTransferTransaction.UserId, decimal.Truncate(moneyTransferTransaction.SendAmount));
                                this._logger.LogError($"Error: {redemptionReversalResult.ErrorMessage}");

                                moneyTransferTransaction.Status = Status.NEEDSMANUALREVERSAL;

                                transaction.Reversal = new TranscationReversal();
                                transaction.Reversal.StatusCode = Status.FAILED.ToString();
                                transaction.Reversal.StatusDescription = "Reversal failed.";
                            }
                            else
                            {

                                moneyTransferTransaction.Status = Status.REVERSED;
                                moneyTransferTransaction.ReversalDate = DateTime.Now;

                                transaction.Reversal = new TranscationReversal();
                                transaction.Reversal.AuthenticationCode = redemptionReversalResult.Data.AuthCode;
                                transaction.Reversal.Amount = TypeUtility.GetDecimalFromString(redemptionReversalResult.Data.Value.Amt);
                                transaction.Reversal.EndBalance = TypeUtility.GetDecimalFromString(redemptionReversalResult.Data.EndBalanace.Amt);
                                transaction.Reversal.Date = DateTime.Now;
                                transaction.Reversal.ReferenceNumber = reversalRequest.ReferenceNumber;
                                transaction.Reversal.StatusCode = redemptionReversalResult.Data.StatusCode;
                                transaction.Reversal.StatusDescription = redemptionReversalResult.Data.Message;
                            }

                            await this._unitOfWork.CommitAsync();
                        }
                        catch (Exception ex)
                        {
                            this._logger.LogError($"Error: {ex.Message}");
                            continue;
                        }
                    }
                }
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> ReverseOnHoldTransactions()
        {
            var checkDate = DateTime.Now.Date.AddDays(-_moneyTransferServiceSettings.ReverseOnHoldMinNoOfDays);
            var onHoldTransactions = await _unitOfWork.MoneyTransferTransactions.FindAsync(f => f.Status == Status.ONHOLD &&
                f.CreatedDate < checkDate,
                withoutTraking: false,
                include => include.User,
                include => include.MoneyTransferBeneficiary,
                include => include.MoneyTransferStatusSteps);

            //Reverse all on hold transactions older than 3 days
            foreach (var item in onHoldTransactions)
            {
                item.Status = BaseEnums.Status.PENDINGREVERSE;
                if (item.User.MoneyTransferProfileStatus != MoneyTransferProfileStatus.Created)
                {
                    item.Remarks = "Profile creation timeout";
                }
                else if (item.MoneyTransferBeneficiary.Status != Status.APPROVED)
                {
                    item.Remarks = "Beneficiary not approved by RAK";
                }

                // Add an entry to money transfer status progress.
                item.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                item.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                {
                    Status = Status.PENDINGREVERSE,
                    Log = item.Remarks,
                    Message = ConstantParam.UM_TransferFailedButWillBeReversed
                });
            }

            await _unitOfWork.CommitAsync();

            //Send the rest of the on hold transactions to external provider
            foreach (var item in onHoldTransactions.Where(x => x.Status == Status.ONHOLD))
            {
                await AddExternalTransaction(item.Id);
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse<MoneyTransferBranch>> GetBranchDetailsByIfscCode(string ifscCode)
        {
            var branch = await this._unitOfWork.MoneyTransferBranches.FirstOrDefaultAsync(
                b => b.PrimaryIdentifierCode == ifscCode && b.Bank.MoneyTransferPartnerId == (int)MoneyTransferPartnerType.DirectClient, b => b.Bank);

            if (branch == null)
            {
                return new ServiceResponse<MoneyTransferBranch>(false, BaseEnums.TransferStatusValidationMessage.IFSCCodeNotExists.ToString());
            }

            // If no validation rules are set on a bank, fallback to original validation rules.
            switch (branch.Bank.CountryCode.Trim().ToUpper())
            {
                case "IN":
                    if (branch.Bank.AccountNumberLengthLimitType == null
                        || branch.Bank.AccountNumberLengthLimitType == AccountNumberLengthLimitType.UseDefault)
                    {
                        branch.Bank.AccountNumberLengthLimitType = AccountNumberLengthLimitType.Range;
                        branch.Bank.AccountNumberLengthLimitValues = string.Join(",", new int[] { 6, 18 });
                    }
                    break;
                case "PK":
                    if (branch.Bank.AccountNumberLengthLimitType == null
                    || branch.Bank.AccountNumberLengthLimitType == AccountNumberLengthLimitType.UseDefault)
                    {
                        branch.Bank.AccountNumberLengthLimitType = AccountNumberLengthLimitType.Range;
                        branch.Bank.AccountNumberLengthLimitValues = string.Join(",", new int[] { 6, 16 });
                    }
                    break;
                default:
                    branch.Bank.AccountNumberLengthLimitType ??= AccountNumberLengthLimitType.UseDefault;
                    break;
            }

            return new ServiceResponse<MoneyTransferBranch>(branch);
        }

        public async Task<ServiceResponse<bool>> EnableGuidedWalkthrough(User user, string languageCode)
        {
            bool enableGuidedWalkthrough = false;
            string nationality = user.CardHolder.Nationality;

            bool hasCreatedBeneficiary = await _unitOfWork.MoneyTransferBeneficiaries.Any(a => a.UserId == user.Id);

            //Check if nationality is enabled
            //Language is enabled
            //has not created any beneficiary
            //App Registration is greater than 
            if (_moneyTransferServiceSettings.GWNationalities.Split('|').Contains(nationality) &&
                _moneyTransferServiceSettings.GWLanguages.Split('|').Contains(languageCode) &&
                !hasCreatedBeneficiary &&
                user.CreatedDate > _moneyTransferServiceSettings.GWStartDate)
            {
                enableGuidedWalkthrough = true;
            }

            return new ServiceResponse<bool>(enableGuidedWalkthrough);
        }

        /// <summary>
        /// Get Repeat Transfers based on user id
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<List<RepeatTransfer>>> GetRepeatTransfers(Guid userId, decimal userBalance)
        {
            List<RepeatTransfer> transfers = new List<RepeatTransfer>();

            var minSendAmount = _moneyTransferServiceSettings.GeneralDefaultAmount;
            var maxRepeatTransfer = _moneyTransferServiceSettings.MaxRepeatTransferCount;

            var minUserBalanceAllowed = _moneyTransferServiceSettings.MinUserBalanceForRepeatTransfer;
            if (userBalance < minUserBalanceAllowed)
            {
                return new ServiceResponse<List<RepeatTransfer>>(transfers);
            }

            var cacheKey = _repeatTransferCacheKeyPrefix + userId.ToString();
            transfers = await _cacheService.GetRecordAsync<List<RepeatTransfer>>(cacheKey);

            var disableCaching = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferDisableRepeatTransferCaching);
            if (transfers is null || disableCaching)
            {
                var recentBeneficiaries = await _unitOfWork.MoneyTransferTransactions.GetRecentSentBeneficiaries(userId, maxRepeatTransfer, minSendAmount);
                var user = await this._unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == userId && x.IsDeleted == false);
                var corridors = await GetCorridors(user.CardHolderId);

                transfers = new List<RepeatTransfer>();

                foreach (var beneficiaryId in recentBeneficiaries)
                {
                    var moneyTransfer = (await _unitOfWork.MoneyTransferTransactions.FindAsync(a => a.MoneyTransferBeneficiaryId == beneficiaryId &&
                                                                                                    a.SendAmount > minSendAmount &&
                                                                                                    a.Status == Status.SUCCESSFUL,
                                                                                               o => o.CreatedDate,
                                                                                               true, null, null, true,
                                                                                               i => i.MoneyTransferBeneficiary)).FirstOrDefault();

                    if (moneyTransfer != null)
                    {
                        var transfer = _mapperGeneral.Map<RepeatTransfer>(moneyTransfer);
                        transfer.Country = corridors.Data.FirstOrDefault(x => x.Code2 == moneyTransfer.MoneyTransferBeneficiary.CountryCode);

                        transfers.Add(transfer);
                    }
                }

                await _cacheService.SetRecordAsync(cacheKey, transfers, TimeSpan.FromDays(60));
            }

            return new ServiceResponse<List<RepeatTransfer>>(transfers);
        }

        /// <summary>
        /// Gets the smart default
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<MoneyTransferSmartDefaultDto>> GetSmartDefault(MoneyTransferSmartDefaultRequestDto request)
        {
            //Setup the general default values
            MoneyTransferSmartDefaultDto result = new MoneyTransferSmartDefaultDto()
            {
                Amount = new AmountDto
                {
                    Amount = decimal.ToDouble(_moneyTransferServiceSettings.GeneralDefaultAmount),
                    Currency = _moneyTransferServiceSettings.GeneralDefaultCurrency
                },
                Type = _moneyTransferServiceSettings.GeneralDefaultType
            };

            //Find the beneficiary details
            var beneficiary = (await _unitOfWork.MoneyTransferBeneficiaries.FindAsync(x => x.Id == request.BeneficiaryId, true, i => i.Country)).FirstOrDefault();

            //If beneficiary is null, and general default is greater than or equal to userBalance, then return the general default
            if (beneficiary == null || _moneyTransferServiceSettings.GeneralDefaultAmount >= request.UserBalance)
            {
                return new ServiceResponse<MoneyTransferSmartDefaultDto>(result);
            }

            //Get SmartDefault Details
            var smartDefaultData = (await _unitOfWork.MoneyTransferSmartDefaults.FindAsync(f => f.CountryCode == beneficiary.CountryCode, true)).FirstOrDefault();
            if (smartDefaultData != null)
            {
                #region Check the conditions to set the smart default amount

                //Initally set the value as the general amount
                var smartDefaultAmount = Convert.ToDecimal(result.Amount.Amount);

                //Check the following criteria
                //A) If the user has NEVER sent money before
                if (!request.HasMoneyTransferTransactions)
                {
                    smartDefaultAmount = smartDefaultData.NeverSentMoneyAmount;
                }
                else
                {
                    var latestTransactionDone = (await _unitOfWork.MoneyTransferTransactions.FindAsync(a =>
                                                                                                   a.MoneyTransferBeneficiaryId == beneficiary.Id
                                                                                                   && a.Status == Status.SUCCESSFUL,
                                                                                                   order: o => o.CreatedDate,
                                                                                                   isDescOrder: true,
                                                                                                   withoutTraking: true,
                                                                                                   skip: 0,
                                                                                                   pageSize: 1)).FirstOrDefault();
                    //B) If the user has sent money before but not to that specific beneficiary
                    if (latestTransactionDone == null)
                    {
                        smartDefaultAmount = smartDefaultData.NeverSentToBeneficiaryAmount;
                    }
                    //C) If the user has sent money before to that beneficiary
                    else if (latestTransactionDone != null)
                    {
                        smartDefaultAmount = latestTransactionDone.ReceiveAmount;
                    }
                }

                #endregion

                #region Check if user balance is sufficient

                decimal transferFees = 0;

                //Initially set the value as smartDefaultAmount
                decimal amountConvertedToSendCurrency = smartDefaultAmount;

                //If the type is Receive, then we need to convert the amount to local currency to check with user balance
                if (smartDefaultData.Type == MoneyTransferSmartDefaultType.Receive.ToString())
                {
                    decimal fxRate = 0;

                    if (beneficiary.TransferType == MoneyTransferType.RAKMoneyCashPayout)
                    {
                        fxRate = beneficiary.Country.CashPickUpLatestRate;
                    }
                    else if (beneficiary.TransferType == MoneyTransferType.OutsideUAE)
                    {
                        fxRate = beneficiary.Country.BankTransferLatestRate;
                    }
                    //TODO: Implement for Wallet later, for now return the general default
                    else
                    {
                        return new ServiceResponse<MoneyTransferSmartDefaultDto>(result);
                    }

                    //Converting to local currency
                    amountConvertedToSendCurrency = smartDefaultAmount * fxRate;
                }

                //If its not first transfer and not loyalty, then we need to set the transfer fee
                if (request.HasMoneyTransferTransactions && !request.IsLoyalty)
                {
                    //Convert enum MoneyTransferType to TransferMethod
                    TransferMethod transferMethod = (TransferMethod)beneficiary.TransferType;

                    //Get transfer fees
                    var transferFeesData = await _lookupService.GetTransferFees(beneficiary.CountryCode,
                                                                                transferMethod,
                                                                                amountConvertedToSendCurrency,
                                                                                beneficiary.UserId);
                    transferFees = transferFeesData?.Data ?? 0;
                }

                //Recheck if the local amount is less than the general amount, then set the general default
                if (amountConvertedToSendCurrency < _moneyTransferServiceSettings.GeneralDefaultAmount)
                {
                    amountConvertedToSendCurrency = _moneyTransferServiceSettings.GeneralDefaultAmount;
                    result.Amount.Amount = decimal.ToDouble(amountConvertedToSendCurrency);

                    return new ServiceResponse<MoneyTransferSmartDefaultDto>(result);
                }

                //Check user balance is sufficient
                if (amountConvertedToSendCurrency + transferFees > request.UserBalance)
                {
                    //Set value by subtracting transfer fees from User balance
                    amountConvertedToSendCurrency = request.UserBalance - transferFees;

                    //Recheck if the local amount is less than the general amount, then set the general default
                    if (amountConvertedToSendCurrency < _moneyTransferServiceSettings.GeneralDefaultAmount)
                    {
                        amountConvertedToSendCurrency = _moneyTransferServiceSettings.GeneralDefaultAmount;
                    }

                    result.Amount.Amount = decimal.ToDouble(amountConvertedToSendCurrency);

                    return new ServiceResponse<MoneyTransferSmartDefaultDto>(result);
                }

                #endregion

                result.Amount.Amount = decimal.ToDouble(smartDefaultAmount);
                result.Amount.Currency = smartDefaultData.Currency;
                result.Type = smartDefaultData.Type;
            }

            return new ServiceResponse<MoneyTransferSmartDefaultDto>(result);
        }

        public async Task<ServiceResponse<MoneyTransferSuspiciousInformation>> SaveSuspiciousInformation(MoneyTransferSuspiciousInformation data)
        {
            //Validations
            var result = await _validator.ValidateAsync(data);
            if (!result.IsValid)
            {
                return new ServiceResponse<MoneyTransferSuspiciousInformation>() { IsSuccessful = false, ErrorMessage = string.Join(",", result.Errors) };
            }

            await _unitOfWork.MoneyTransferSuspiciousInformations.AddAsync(data);

            try
            {
                await _unitOfWork.CommitAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in saving suspicious information for {beneficiaryId}", data.MoneyTransferBeneficiaryId);
                throw;
            }

            return new ServiceResponse<MoneyTransferSuspiciousInformation>(data);
        }

        public async Task<ServiceResponse<MoneyTransferSuspiciousInformation>> UpdateSuspiciousInformation(MoneyTransferSuspiciousInformation data)
        {
            //Validations
            var result = await _validator.ValidateAsync(data);
            if (!result.IsValid)
            {
                return new ServiceResponse<MoneyTransferSuspiciousInformation>() { IsSuccessful = false, ErrorMessage = string.Join(",", result.Errors) };
            }

            var additionalInformation = await _unitOfWork.MoneyTransferSuspiciousInformations.FirstOrDefaultAsync(f => f.Id == data.Id);
            if (additionalInformation == null)
            {
                return new ServiceResponse<MoneyTransferSuspiciousInformation>(false, "No Record found");
            }

            additionalInformation.NationalIdNo = data.NationalIdNo;
            additionalInformation.FullName = data.FullName;
            additionalInformation.DateOfBirth = data.DateOfBirth;
            additionalInformation.CityId = data.CityId;

            try
            {
                await _unitOfWork.CommitAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in updating suspicious information for {beneficiaryId}", data.MoneyTransferBeneficiaryId);
                throw;
            }

            return new ServiceResponse<MoneyTransferSuspiciousInformation>(additionalInformation);
        }

        public async Task<ServiceResponse<bool>> ApproveSuspiciousInformation(Guid id)
        {
            var additionalInformation = await _unitOfWork.MoneyTransferSuspiciousInformations.FirstOrDefaultAsync(f => f.Id == id);
            if (additionalInformation == null)
            {
                return new ServiceResponse<bool>(false, "No Record found");
            }

            additionalInformation.IsApproved = true;
            additionalInformation.ApprovalDateTime = DateTime.Now;

            try
            {
                await _unitOfWork.CommitAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in approving suspicious information for {id}", additionalInformation.MoneyTransferBeneficiaryId);
                throw;
            }

            return new ServiceResponse<bool>(true);
        }

        public async Task<bool> HasOldTransactions(string emiratesId, Guid excludedUserId)
        {
            return await _unitOfWork.MoneyTransferTransactions.Any(t => t.IsDeleted == false && t.UserId != excludedUserId && t.MoneyTransferBeneficiary.DocumentNumber == emiratesId && t.Status == Status.SUCCESSFUL);
        }

        private void Log(string message)
        {
            message = $"MoneyTransferService :: {message}";
            _logger.LogDebug(message);
        }

        public async Task<ServiceResponse> ClearCache(string key)
        {
            try
            {
                await _cacheService.RemoveAsync(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ConstantParam.UnableToDeleteCacheKey, key);
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse<IEnumerable<RemittanceDestinationDto>>> GetCorridors(string cardholderId)
        {
            var rateExpiryInMinutes = _moneyTransferServiceSettings.RateExpiryInMinutes;

            // Find cardholder.
            var cardholder = await this._unitOfWork.CardHolders.FirstOrDefaultAsync(x => x.Id == cardholderId);

            IList<RemittanceDestinationDto> destinationsDto;
            if (_moneyTransferServiceSettings.CorridorsCorporateId == "99998" && cardholder.CorporateId == "99998")
            {
                // Get records from cache.
                destinationsDto = await this._cacheService.GetRecordAsync<IList<RemittanceDestinationDto>>(_remittanceDestinationCacheKey + "99998");
            }
            else
            {
                // Get records from cache.
                destinationsDto = await this._cacheService.GetRecordAsync<IList<RemittanceDestinationDto>>(_remittanceDestinationCacheKey);
            }


            // If nothing was found, get data from DB and cache it.
            if (destinationsDto is null || destinationsDto.Count == 0)
            {
                List<RemittanceDestination> destinations;
                if (_moneyTransferServiceSettings.CorridorsCorporateId == "99998" && cardholder.CorporateId == "99998")
                {
                    var criteria = new List<Expression<Func<RemittanceDestination, bool>>>
                    {
                    };

                    destinations = await _unitOfWork.RemittanceDestinations.Search(criteria);

                    // Remove non active providers and money transfer methods.
                    destinations = destinations.Where(x => x.IsActive).ToList();
                    foreach (var destination in destinations)
                    {
                        destination.MoneyTransferMethods = destination.MoneyTransferMethods.Where(x => x.IsActive && x.MoneyTransferProvider.IsActive).ToList();
                    }

                    destinationsDto = (IList<RemittanceDestinationDto>)_mapper.Map<IEnumerable<RemittanceDestination>, IEnumerable<RemittanceDestinationDto>>(destinations);

                    await this._cacheService.SetRecordAsync(_remittanceDestinationCacheKey + "99998", destinationsDto, TimeSpan.FromMinutes(rateExpiryInMinutes));
                }
                else
                {
                    var criteria = new List<Expression<Func<RemittanceDestination, bool>>>
                    {
                        x => x.IsActive,
                        x => x.MoneyTransferMethods.Any(x => x.IsActive),
                        x => x.MoneyTransferMethods.Any(x => x.MoneyTransferProvider.IsActive)
                    };

                    destinations = await _unitOfWork.RemittanceDestinations.Search(criteria);

                    // Remove non active providers and money transfer methods.
                    destinations = destinations.Where(x => x.IsActive).ToList();
                    foreach (var destination in destinations)
                    {
                        destination.MoneyTransferMethods = destination.MoneyTransferMethods.Where(x => x.IsActive && x.MoneyTransferProvider.IsActive).ToList();
                    }

                    destinationsDto = (IList<RemittanceDestinationDto>)_mapper.Map<IEnumerable<RemittanceDestination>, IEnumerable<RemittanceDestinationDto>>(destinations);

                    await this._cacheService.SetRecordAsync(_remittanceDestinationCacheKey, destinationsDto, TimeSpan.FromMinutes(rateExpiryInMinutes));
                }
            }

            if (destinationsDto != null && destinationsDto.Count > 0)
            {
                // Order destinations.
                destinationsDto = destinationsDto.OrderBy(x => x.DisplayOrder).ToList();

                foreach (var destinationDto in destinationsDto)
                {
                    if (destinationDto.MoneyTransferMethods != null && destinationDto.MoneyTransferMethods.Count > 0)
                    {
                        // Order money transfer methods.
                        destinationDto.MoneyTransferMethods = destinationDto.MoneyTransferMethods.OrderBy(x => x.DisplayOrder).ToList();
                    }
                }
            }

            return new ServiceResponse<IEnumerable<RemittanceDestinationDto>>(destinationsDto);
        }

        public async Task<ServiceResponse<IEnumerable<FieldGroupDto>>> GetFieldGroups(int deliveryMethodId, string languageCode, CardHolder cardholder)
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                languageCode = "en";
            }

            // Find all translations.
            var translationsCached = await this._cacheService.GetRecordAsync<IList<MoneyTransferTextLocalization>>(_moneyTransferTextLocalizationCacheKey + "_" + languageCode);
            if (translationsCached is null || translationsCached.Count == 0)
            {
                translationsCached = await this._unitOfWork.MoneyTransferTextLocalizations.FindAsync(x => x.LanguageCode == languageCode);
                await this._cacheService.SetRecordAsync(_moneyTransferTextLocalizationCacheKey + "_" + languageCode, translationsCached, TimeSpan.FromDays(30));
            }

            IList<FieldGroupDto> fieldGroupsDto = null;

            // Check if they are part of the experiment.
            // var experimentUser = await _unitOfWork.ExperimentUsers.FirstOrDefaultAsync(f => f.ExperimentId == (int)ExperimentType.PersonalDetailsRedesign && f.CardHolderId == cardholder.Id && !f.IsDeleted);
            // if (experimentUser != null && experimentUser.GroupCode == "A")
            // {
            //     // We will only have 2 groups, "A": Show the new screens, "B": Show the old ones.
            //     fieldGroupsDto = await this._cacheService.GetRecordAsync<IList<FieldGroupDto>>(_fieldGroupsCacheKey + deliveryMethodId + languageCode + "EXP");

            //     // If nothing was found, get data from DB and cache it.
            //     if (fieldGroupsDto is null || fieldGroupsDto.Count == 0)
            //     {

            //         var criteria = new List<Expression<Func<FieldGroup, bool>>>()
            //         {
            //             x => x.MoneyTransferMethodId == deliveryMethodId,
            //             x => x.IsExperimental == true
            //         };


            //         var fieldGroups = await _unitOfWork.FieldGroups.Search(criteria);
            //         fieldGroupsDto = (IList<FieldGroupDto>)_mapper.Map<IEnumerable<FieldGroup>, IEnumerable<FieldGroupDto>>(fieldGroups);

            //     }
            // }

            // Check if they are part of the full name experiment
            bool isFullNameExperiment = false;
            string fullNameExperimentCacheKey = $"{_fieldGroupsCacheKey}{deliveryMethodId}{languageCode}FULLNAME_EXP";
            var isFeatureEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferAccountNameForIndiaPakistanNepal);
            if (isFeatureEnabled)
            {
                var fullNameExperiment = await _unitOfWork.Experiments.FirstOrDefaultAsync(x => x.Id == (int)ExperimentType.PersonalDetails_FullName && !x.IsDeleted);
                if (fullNameExperiment != null && (await fullNameExperiment.IsUserPartOfExperiment(cardholder.Id, _unitOfWork.ExperimentUsers)))
                {
                    var moneyTransferMethod = await _unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.Id == deliveryMethodId, a => a.RemittanceDestination, a => a.MoneyTransferProvider);
                    if (moneyTransferMethod?.RemittanceDestination != null &&
                        moneyTransferMethod.MoneyTransferProvider != null &&
                        moneyTransferMethod.MoneyTransferProvider.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer &&
                        new[] { "IND", "PAK", "NPL" }.Contains(moneyTransferMethod.RemittanceDestination.Code3))
                    {
                        fieldGroupsDto = await this._cacheService.GetRecordAsync<IList<FieldGroupDto>>(fullNameExperimentCacheKey);
                        if (fieldGroupsDto is null || fieldGroupsDto.Count == 0)
                        {
                            var criteria = new List<Expression<Func<FieldGroup, bool>>>
                            {
                                x => x.MoneyTransferMethodId == deliveryMethodId,
                                x => x.IsExperimental == true
                            };
                            var fieldGroups = await _unitOfWork.FieldGroups.Search(criteria);
                            fieldGroupsDto = (IList<FieldGroupDto>)_mapper.Map<IEnumerable<FieldGroup>, IEnumerable<FieldGroupDto>>(fieldGroups);
                        }
                        isFullNameExperiment = true;
                    }
                }
            }

            // If user was not part of the experiment or no fields were found, find the old fields.
            if (fieldGroupsDto is null || fieldGroupsDto.Count == 0)
            {
                // Get records from cache.
                fieldGroupsDto = await this._cacheService.GetRecordAsync<IList<FieldGroupDto>>(_fieldGroupsCacheKey + deliveryMethodId + languageCode);

                // If nothing was found, get data from DB and cache it.
                if (fieldGroupsDto is null || fieldGroupsDto.Count == 0)
                {

                    var criteria = new List<Expression<Func<FieldGroup, bool>>>()
                    {
                        x => x.MoneyTransferMethodId == deliveryMethodId,
                        x => x.IsExperimental == false
                    };

                    var fieldGroups = await _unitOfWork.FieldGroups.Search(criteria);
                    fieldGroupsDto = (IList<FieldGroupDto>)_mapper.Map<IEnumerable<FieldGroup>, IEnumerable<FieldGroupDto>>(fieldGroups);

                }
            }

            // Apply translations.
            foreach (var fieldGroupDto in fieldGroupsDto)
            {
                var title = translationsCached.FirstOrDefault(x => x.StringKey == fieldGroupDto.Title);
                fieldGroupDto.Title = title == null ? fieldGroupDto.Title : title.Translation;

                var description = translationsCached.FirstOrDefault(x => x.StringKey == fieldGroupDto.Description);
                fieldGroupDto.Description = description == null ? fieldGroupDto.Description : description.Translation;

                foreach (var navigationField in fieldGroupDto.NavigationFields)
                {
                    var navigationFieldTitle = translationsCached.FirstOrDefault(x => x.StringKey == navigationField.Title);
                    navigationField.Title = navigationFieldTitle == null ? navigationField.Title : navigationFieldTitle.Translation;

                    var navigationFieldDescription = translationsCached.FirstOrDefault(x => x.StringKey == navigationField.Description);
                    navigationField.Description = navigationFieldDescription == null ? navigationField.Description : navigationFieldDescription.Translation;
                }

                foreach (var banner in fieldGroupDto.Banners)
                {
                    var bannerTitle = translationsCached.FirstOrDefault(x => x.StringKey == banner.Title);
                    banner.Title = bannerTitle == null ? banner.Title : bannerTitle.Translation;

                    var bannerDescription = translationsCached.FirstOrDefault(x => x.StringKey == banner.Description);
                    banner.Description = bannerDescription == null ? banner.Description : bannerDescription.Translation;
                }

                foreach (var field in fieldGroupDto.InputFields)
                {
                    var label = translationsCached.FirstOrDefault(x => x.StringKey == field.Label);
                    field.Label = label == null ? field.Label : label.Translation;

                    var placeholder = translationsCached.FirstOrDefault(x => x.StringKey == field.Placeholder);
                    field.Placeholder = placeholder == null ? field.Placeholder : placeholder.Translation;
                }

                // highlight account number
                var targetedNationalities = new List<string>
                {
                    "IND", "PAK", "NPL"
                };
                //for cache handling
                if (await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferHighlightAccountNumber))
                {
                    if (fieldGroupDto.GwIdentifier == "AddBeneficiary_AccountNumber" || fieldGroupDto.GwIdentifier == "AddBeneficiary_AccountNumber_Highlighted")
                    {
                        if (targetedNationalities.Contains(cardholder.Nationality))
                        {
                            fieldGroupDto.GwIdentifier = "AddBeneficiary_AccountNumber_Highlighted";
                            fieldGroupDto.HighlightedField = "MT_BeneficiaryAccountNumber_Input_AccNumber";
                        }
                        else
                        {
                            fieldGroupDto.GwIdentifier = "AddBeneficiary_AccountNumber";
                            fieldGroupDto.HighlightedField = null;
                        }
                    }
                }
                else
                {
                    if (fieldGroupDto.GwIdentifier == "AddBeneficiary_AccountNumber_Highlighted")
                    {
                        fieldGroupDto.GwIdentifier = "AddBeneficiary_AccountNumber";
                        fieldGroupDto.HighlightedField = null;
                    }
                }


            }

            if (isFullNameExperiment)
            {
                await this._cacheService.SetRecordAsync(fullNameExperimentCacheKey, fieldGroupsDto, TimeSpan.FromDays(30));
            }
            else
            {
                await this._cacheService.SetRecordAsync(_fieldGroupsCacheKey + deliveryMethodId + languageCode, fieldGroupsDto, TimeSpan.FromDays(30));
            }

            if (fieldGroupsDto != null && fieldGroupsDto.Count > 0)
            {
                // Order fields.
                fieldGroupsDto = fieldGroupsDto.OrderBy(x => x.DisplayOrder).ToList();

                foreach (var fieldGroupDto in fieldGroupsDto)
                {
                    if (fieldGroupDto.InputFields != null && fieldGroupDto.InputFields.Count > 0)
                    {
                        // Order fields.
                        fieldGroupDto.InputFields = fieldGroupDto.InputFields.OrderBy(x => x.DisplayOrder).ToList();

                        // Order validation rules.
                        foreach (var field in fieldGroupDto.InputFields)
                        {
                            field.ValidationRules = field.ValidationRules.OrderBy(x => x.CharacterTypes.Count).ToList();
                        }
                    }
                }
            }

            return new ServiceResponse<IEnumerable<FieldGroupDto>>(fieldGroupsDto);
        }



        public async Task<ServiceResponse<ReviewBeneficiaryDto>> ValidateBeneficiary(PostBeneficiaryRequest request, string languageCode)
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                languageCode = "en";
            }

            // Build beneficiary.
            var tryBuildBeneficiary = await BuildBeneficiary(request);

            if (tryBuildBeneficiary.IsSuccessful == false)
            {
                return new ServiceResponse<ReviewBeneficiaryDto>()
                {
                    Data = new ReviewBeneficiaryDto()
                    {
                        Successful = false,
                        Error = new ErrorInformation()
                        {
                            Message = tryBuildBeneficiary.ErrorMessage
                        }
                    }
                };
            }

            // Create summary for the beneficiary.
            var result = new ReviewBeneficiaryDto()
            {
                Successful = true,
                BeneficarySummary = new List<BeneficarySummary>()
            };

            var tryGenerateBeneficiarySummaryScreenDetails = await GenerateBeneficiarySummaryScreenDetails(tryBuildBeneficiary.Data, languageCode);
            if (tryGenerateBeneficiarySummaryScreenDetails.IsSuccessful == false)
            {
                return new ServiceResponse<ReviewBeneficiaryDto>(false, tryGenerateBeneficiarySummaryScreenDetails.ErrorMessage);
            }

            result.BeneficarySummary = tryGenerateBeneficiarySummaryScreenDetails.Data;

            return new ServiceResponse<ReviewBeneficiaryDto>(result);
        }

        public async Task<ServiceResponse<MoneyTransferBeneficiary>> AddBeneficiary(PostBeneficiaryRequest request)
        {
            var beneficiaryAlreadyExists = false;
            var newBeneficiaryId = new Guid();

            // Build beneficiary.
            var tryBuildBeneficiary = await BuildBeneficiary(request);
            if (tryBuildBeneficiary.IsSuccessful == false)
            {
                //Incase there is a duplicate we set the newBeneficiaryId from the existingBeneficiaryId
                if (tryBuildBeneficiary.ErrorMessage == TransferStatusValidationMessage.BeneficiaryAlreadyExists.ToString() &&
                    ExistingBeneficiaryId != null)
                {
                    beneficiaryAlreadyExists = true;
                    newBeneficiaryId = ExistingBeneficiaryId;
                }
                else
                {
                    return new ServiceResponse<MoneyTransferBeneficiary>(false, tryBuildBeneficiary.ErrorMessage);
                }
            }
            else
            {
                await _unitOfWork.MoneyTransferBeneficiaries.AddAsync(tryBuildBeneficiary.Data);
                await _unitOfWork.CommitAsync();

                newBeneficiaryId = tryBuildBeneficiary.Data.Id;

                var cacheKey = $"{_beneficiaryListCacheKeyPrefix}{tryBuildBeneficiary.Data.UserId}_";
                await ClearCache(cacheKey);
            }


            // Get and return beneficiary.
            var createdBeneficiary = await _unitOfWork.MoneyTransferBeneficiaries.FirstOrDefaultAsync(b => b.Id == newBeneficiaryId,
                                                                                                      include => include.MoneyTransferReason,
                                                                                                      include => include.Country,
                                                                                                      include => include.LinkedUser,
                                                                                                      include => include.ExternalBeneficiary,
                                                                                                      include => include.BeneficiaryAdditionalFields);
            //Only send beneficiary data if its a new beneficiary
            if (!beneficiaryAlreadyExists)
            {
                var user = await this._unitOfWork.Users.FirstOrDefaultAsync(u => u.Id == request.UserId);

                // Add beneficiary to external service provider.
                if (user.MoneyTransferProfileStatus == MoneyTransferProfileStatus.Created)
                {
                    var transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.Id == request.MoneyTransferMethodId,
                                                                                                          include => include.MoneyTransferProvider);
                    if (transferMethod.MoneyTransferProvider.WUAddBeneficiaryEnabled)
                    {
                        var tryAddExternal = await this._moneyTransferBeneficiaryService.AddExternalProviderBeneficiary(createdBeneficiary.Id);
                        if (tryAddExternal.IsSuccessful == false)
                        {
                            // TODO-Raed: Should we delete the beneficiary here?
                            return new ServiceResponse<MoneyTransferBeneficiary>(false, tryAddExternal.ErrorMessage);
                        }
                    }
                    else
                    {
                        await _moneyTransferBeneficiaryService.AddExternalBeneficiary(createdBeneficiary.Id);
                    }
                }
                // Send SMS confirming adding the beneficiary.
                else
                {
                    await _textMessageSenderService.SendMTBeneficiaryCreatedMessage(user.PhoneNumber.ToShortPhoneNumber(), createdBeneficiary.FullName);
                }
            }

            return new ServiceResponse<MoneyTransferBeneficiary>(createdBeneficiary);
        }

        public async Task<ServiceResponse<ReviewTransferDto>> ValidateTransfer(PostTransferDto transferRequestDto, string languageCode, User user)
        {
            transferRequestDto.SendAmount.Amount = Math.Round(transferRequestDto.SendAmount.Amount, 2);

            if (string.IsNullOrEmpty(languageCode))
            {
                languageCode = "en";
            }

            var isValid = await ValidateTransferDetails(transferRequestDto);

            if (isValid.IsSuccessful == false)
            {
                return new ServiceResponse<ReviewTransferDto>(false, isValid.ErrorMessage);
            }

            var beneficiary = await _unitOfWork.MoneyTransferBeneficiaries.FirstOrDefaultAsync(x => x.Id == transferRequestDto.BeneficiaryId,
                                                                                               include => include.BeneficiaryAdditionalFields,
                                                                                               include => include.ExternalBeneficiary,
                                                                                               include => include.MoneyTransferTransactions);

            // Create summary for the transfer.
            var result = new ReviewTransferDto()
            {
                Successful = true,
                BeneficarySummary = new Dictionary<string, string>(),
                TransferSummary = new Dictionary<string, string>(),
            };

            // Initialize invalid amount error variable
            InValidSendAmountError invalidAmountError = null;

            var tryGenerateBeneficiarySummaryScreenDetails = await GenerateBeneficiarySummaryScreenDetails(beneficiary, languageCode);
            if (tryGenerateBeneficiarySummaryScreenDetails.IsSuccessful == false)
            {
                return new ServiceResponse<ReviewTransferDto>(false, tryGenerateBeneficiarySummaryScreenDetails.ErrorMessage);
            }


            // Convert result to dictionary.
            foreach (var item in tryGenerateBeneficiarySummaryScreenDetails.Data)
            {
                result.BeneficarySummary.Add(item.Label, item.Value);
            }


            // Find all translations.
            var translationsCached = await _cacheService.GetRecordAsync<IList<MoneyTransferTextLocalization>>(_moneyTransferTextLocalizationCacheKey + "_" + languageCode);
            if (translationsCached is null || translationsCached.Count == 0)
            {
                translationsCached = await _unitOfWork.MoneyTransferTextLocalizations.FindAsync(x => x.LanguageCode == languageCode);
                await this._cacheService.SetRecordAsync(_moneyTransferTextLocalizationCacheKey + "_" + languageCode, translationsCached, TimeSpan.FromDays(30));
            }

            MoneyTransferMethodType transferType = MoneyTransferMethodType.BankTransfer;
            TransferMethod transferMethod = TransferMethod.BANKTRANSFER;

            switch (beneficiary.TransferType)
            {
                case MoneyTransferType.OutsideUAE:
                    transferType = MoneyTransferMethodType.BankTransfer;
                    transferMethod = TransferMethod.BANKTRANSFER;
                    break;
                case MoneyTransferType.RAKMoneyCashPayout:
                    transferType = MoneyTransferMethodType.CashPickup;
                    transferMethod = TransferMethod.CASHPICKUP;
                    break;
                case MoneyTransferType.Wallet:
                    transferType = MoneyTransferMethodType.Wallet;
                    transferMethod = TransferMethod.WALLET;
                    break;
                default:
                    break;
            }

            var provider = await _unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.MoneyTransferMethodType == transferType && x.CountryCode == beneficiary.CountryCode);

            var transferSummary = new Dictionary<string, string>();

            // Date.
            var dateKey = translationsCached.FirstOrDefault(x => x.StringKey == "MoneyTransferSummary_Date");
            if (dateKey is null)
            {
                transferSummary.Add("Date:", DateTime.Now.ToString("dd MMMM yyyy"));
            }
            else
            {
                transferSummary.Add($"{dateKey.Translation}:", DateTime.Now.ToString("dd MMMM yyyy"));
            }

            // Transfer amount.
            var transferAmountKey = translationsCached.FirstOrDefault(x => x.StringKey == "MoneyTransferSummary_TransferAmount");
            if (transferAmountKey is null)
            {
                transferSummary.Add("Transfer Amount:", $"{transferRequestDto.SendAmount.Amount} AED");
            }
            else
            {
                transferSummary.Add($"{transferAmountKey.Translation}:", $"{transferRequestDto.SendAmount.Amount} AED");
            }


            FxRateConversionResponseRakModel rate = null;
            FreeTransferDetails freeTransferDetails = null;
            ServiceResponse<decimal?> transferFees = null;

            if (string.IsNullOrEmpty(transferRequestDto.ConvertedAmount) || string.IsNullOrEmpty(transferRequestDto.Rate) || string.IsNullOrEmpty(transferRequestDto.Fee))
            {
                var tryGetFxRates = await GetFxRates(transferRequestDto.UserId, provider.Currency, (decimal)transferRequestDto.SendAmount.Amount, transferType.ToString());
                if (tryGetFxRates.IsSuccessful == false)
                {
                    return new ServiceResponse<ReviewTransferDto>(false, tryGetFxRates.ErrorMessage);
                }
                rate = tryGetFxRates.Data.FxRates.First();
                result.IsFirstTransfer = tryGetFxRates.Data.IsFirstTransfer;
            }
            else
            {
                var disableLoyaltyWaive = await _featureManager.IsEnabledAsync(FeatureFlags.Mt_DisableLoyaltyWaive);
                freeTransferDetails = await this._unitOfWork.MoneyTransferTransactions.GetFreeTransferDetails(transferRequestDto.UserId,
                                                                                                              _rakSettings.LoyaltyLimitAmount,
                                                                                                              Convert.ToDateTime(_rakSettings.LoyaltyImplementDate),
                                                                                                              _rakSettings.LoyaltyLimitCount,
                                                                                                              disableLoyaltyWaive);

                transferFees = await _lookupService.GetTransferFees(beneficiary.CountryCode, transferMethod, (decimal)transferRequestDto.SendAmount.Amount, beneficiary.UserId);
                result.IsFirstTransfer = freeTransferDetails.FreeFirstTransfer;
            }

            // Received amount.
            if (string.IsNullOrEmpty(transferRequestDto.ConvertedAmount))
            {
                var totalCredit = rate.FxConversionRates.First().ConvertedAmount;
                var receiveAmountKey = translationsCached.FirstOrDefault(x => x.StringKey == "MoneyTransferSummary_ReceiveAmount");
                if (receiveAmountKey is null)
                {
                    transferSummary.Add("Received Amount: ", $"{totalCredit} {rate.ToCurrency}");
                }
                else
                {
                    transferSummary.Add($"{receiveAmountKey.Translation}", $"{totalCredit} {rate.ToCurrency}");
                }
            }
            else
            {
                var totalCredit = transferRequestDto.ConvertedAmount;
                var receiveAmountKey = translationsCached.FirstOrDefault(x => x.StringKey == "MoneyTransferSummary_ReceiveAmount");
                if (receiveAmountKey is null)
                {
                    transferSummary.Add("Received Amount: ", $"{totalCredit} {provider.Currency}");
                }
                else
                {
                    transferSummary.Add($"{receiveAmountKey.Translation}", $"{totalCredit} {provider.Currency}");
                }
            }


            // Fee.
            decimal fee = 0;
            if (string.IsNullOrEmpty(transferRequestDto.Fee))
            {
                fee = Convert.ToDecimal(rate.ConversionCharges.TotalCharges);
                transferSummary.Add("Fee (Inclusive VAT):", $"{fee} AED");
            }
            else
            {
                fee = Convert.ToDecimal(transferRequestDto.Fee);
                if (transferFees != null && transferFees.Data.HasValue)
                {
                    fee = Convert.ToDecimal(transferFees.Data);
                }

                if (freeTransferDetails.FreeFirstTransfer || freeTransferDetails.FreeLoyaltyTransfer)
                {
                    fee = 0;
                }

                transferSummary.Add("Fee (Inclusive VAT):", $"{fee} AED");
            }

            // Rate.
            if (string.IsNullOrEmpty(transferRequestDto.Rate))
            {
                var rateValue = Math.Round(1 / Convert.ToDecimal(rate.FxConversionRates.First().Rate), 7);
                var fxRateValue = $"{rateValue}";
                transferSummary.Add("Rate:", fxRateValue);
                result.LogFxRate = fxRateValue;
            }
            else
            {
                var fxRateValue = $"{transferRequestDto.Rate}";
                transferSummary.Add("Rate:", fxRateValue);
                result.LogFxRate = fxRateValue;
            }

            // Transfer method.
            var transferMethodLabel = string.Empty;

            switch (beneficiary.TransferType)
            {
                case MoneyTransferType.OutsideUAE:
                    transferMethodLabel = "Bank Transfer"; break;
                case MoneyTransferType.RAKMoneyCashPayout:
                    transferMethodLabel = "Cash Pickup"; break;
                case MoneyTransferType.Wallet:
                    transferMethodLabel = "Wallet"; break;
                default:
                    break;
            }

            transferSummary.Add("Transfer Method:", $"{transferMethodLabel}");

            // If WU rates are not enabled, add a "Receive in" entry to the transfer summary
            //if (provider.WUGetRatesEnabled == false)
            //{
            //    var receiveInKey = translationsCached.FirstOrDefault(x => x.StringKey == "ReceiveIn");
            //    if (receiveInKey is null)
            //    {
            //        transferSummary.Add("Receive in:", "1 min");
            //    }
            //    else
            //    {
            //        transferSummary.Add($"{receiveInKey.Translation}: ", "1 min");
            //    }
            //}

            // Total.
            var totalAmount = (decimal)transferRequestDto.SendAmount.Amount + Convert.ToDecimal(fee);
            var totalKey = translationsCached.FirstOrDefault(x => x.StringKey == "MoneyTransferSummary_Total");
            if (totalKey is null)
            {
                transferSummary.Add("Total:", $"{totalAmount} AED");
            }
            else
            {
                transferSummary.Add($"{totalKey.Translation}:", $"{totalAmount} AED");
            }

            result.TransferSummary = transferSummary;

            result.IsFirstTransferForBeneficiary = !beneficiary.MoneyTransferTransactions.Any();

            // Validate Transfer (only For WU)
            if (provider.WUValidateTransferEnabled && beneficiary.TransferType != MoneyTransferType.DirectTransfer && beneficiary.ExternalBeneficiary != null)
            {
                try
                {
                    var trxrefno = TypeUtility.GetReferenceNumber(Convert.ToString(TransactionPrefix.RMT), 12);
                    while (await _unitOfWork.Transactions.Any(t => t.ReferenceNumber == trxrefno))
                    {
                        trxrefno = TypeUtility.GetReferenceNumber(Convert.ToString(TransactionPrefix.RMT), 12);
                    }
                    var createTransferRequest = new CreateTransferRequest()
                    {
                        TransactionType = TransactionType.RAKMoney.ToString(),
                        PurposeCode = beneficiary.MoneyTransferReasonId == null ? "8" : beneficiary.MoneyTransferReasonId.ToString(),
                        Sender = new MoneyTransferSender()
                        {
                            CustomerIdType = "Emirates_ID",
                            CustomerIdNumber = beneficiary.DocumentNumber,
                            PlaceOfBirth = await GetPlaceOfBirthAsync(user.CardHolder, beneficiary.CountryCode),
                            Nationality = await GetTwoLetterNationalityCodeAsync(user.CardHolder.Nationality, beneficiary.CountryCode),
                            DateOfBirth = await GetDateOfBirthAsync(user.CardHolder, beneficiary.CountryCode)
                        },
                        Receiver = new MoneyTransferReceiver()
                        {
                            BeneficiaryId = beneficiary.ExternalBeneficiary.ExternalId
                        },
                        Amount = new MoneyTransferAmount()
                        {
                            Currency = ConstantParam.DefaultCurrency.ToString(),
                            Amount = transferRequestDto.SendAmount.Amount.ToString(),
                        },
                        ApiVersion = "2.4.0",
                        C3ReferenceNumber = trxrefno,
                        MessageId = Guid.NewGuid().ToString(),
                        PreValidateFlag = true
                    };
                    var externalValidateResult = await this._externalProviderMoneyTransferService.SendMoneyTransfer(createTransferRequest);
                    result.Set(true, externalValidateResult.IsSuccessful, externalValidateResult.ErrorMessage);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in ValidateTransfer");
                    result.Set(false, false, ReviewTransferDto.TECHNICAL_ERROR_CODE); // Defaulting to technical error
                }
            }
            else
                result.Set(false, false, null);

            // Calculate max sendable amount and check for invalid send amount scenarios
            var tryGetBalance = await this.GetBalance(user);
            if (tryGetBalance.IsSuccessful)
            {
                var transferMethodForFees = TransferMethod.BANKTRANSFER;
                switch (beneficiary.TransferType)
                {
                    case MoneyTransferType.OutsideUAE:
                        transferMethodForFees = TransferMethod.BANKTRANSFER;
                        break;
                    case MoneyTransferType.RAKMoneyCashPayout:
                        transferMethodForFees = TransferMethod.CASHPICKUP;
                        break;
                    case MoneyTransferType.Wallet:
                        transferMethodForFees = TransferMethod.WALLET;
                        break;
                }

                var (maxSendableAmount, calculatedInvalidAmountError) = await CalculateMaxSendableAmount(
                    tryGetBalance.Data,
                    beneficiary.CountryCode,
                    transferMethodForFees,
                    beneficiary.UserId,
                    (decimal)transferRequestDto.SendAmount.Amount);

                invalidAmountError = calculatedInvalidAmountError;
            }

            // Set the new invalid send amount error properties
            result.IsInValidSendAmountError = invalidAmountError != null;
            result.InValidSendAmountError = invalidAmountError;

            return new ServiceResponse<ReviewTransferDto>(result);
        }
        public async Task<Tuple<bool, MoneyTransferType?>> GetBeneficiaryDetailsToPromptOtp(Guid beneficiaryId)
        {
            var isFirstTransfer = false;
            var beneficiaryDetails = await _unitOfWorkReadOnly.MoneyTransferBeneficiaries.FirstOrDefaultAsync(x => x.Id == beneficiaryId && !x.IsDeleted);

            if (beneficiaryDetails != null
                && beneficiaryDetails.TransferType != MoneyTransferType.RAKMoneyCashPayout
                && beneficiaryDetails.TransferType != MoneyTransferType.Wallet)
            {
                isFirstTransfer = !await _unitOfWorkReadOnly.MoneyTransferTransactions.Any(x => x.MoneyTransferBeneficiaryId == beneficiaryId);
            }


            return Tuple.Create(isFirstTransfer, beneficiaryDetails?.TransferType);
        }

        private async Task<ServiceResponse<ReviewTransferDto>> ValidateTransferDetails(PostTransferDto transferRequestDto)
        {
            // Validate user.
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == transferRequestDto.UserId && !x.IsBlocked, u => u.CardHolder);
            if (user is null)
            {
                return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.UserNotExists.ToString());
            }

            if (user.ApplicationId == MobileApplicationId.C3Pay && user.CardHolder.BelongsToExchangeHouse)
            {
                return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.ExchangeHouseUser.ToString());
            }




            // Validate beneficiary.
            var beneficiary = await _unitOfWork.MoneyTransferBeneficiaries.FirstOrDefaultAsync(x => x.Id == transferRequestDto.BeneficiaryId
                                                                                                    && x.UserId == transferRequestDto.UserId
                                                                                                    && x.IsDeleted == false);
            if (beneficiary is null)
            {
                return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.BeneficiaryNotExists.ToString());
            }

            if (transferRequestDto.SendAmount.Amount <= 0)
            {
                return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.AmountNotExists.ToString());
            }


            // Try find provider.
            MoneyTransferProvider provider = null;

            TransferMethod transferMethodForFees = TransferMethod.BANKTRANSFER;
            switch (beneficiary.TransferType)
            {
                case MoneyTransferType.OutsideUAE:
                    transferMethodForFees = TransferMethod.BANKTRANSFER;
                    provider = await _unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.CountryCode == beneficiary.CountryCode
                    && x.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer);
                    break;
                case MoneyTransferType.RAKMoneyCashPayout:
                    transferMethodForFees = TransferMethod.CASHPICKUP;
                    provider = await _unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.CountryCode == beneficiary.CountryCode
                    && x.MoneyTransferMethodType == MoneyTransferMethodType.CashPickup);

                    break;
                case MoneyTransferType.Wallet:
                    transferMethodForFees = TransferMethod.WALLET;
                    provider = await _unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.CountryCode == beneficiary.CountryCode
                    && x.MoneyTransferMethodType == MoneyTransferMethodType.Wallet);
                    break;
                default:
                    break;
            }


            if (provider != null && provider.WUSendMoneyEnabled)
            {
                int wuConfigGraceDays = _moneyTransferServiceSettings.WUEmiratesIdExpiryGracePeriodDays;
                if (user.CardHolder is null || (user.CardHolder.EmiratesIdExpiryDate.HasValue &&
                    DateTime.Now.Date >= Convert.ToDateTime(user.CardHolder.EmiratesIdExpiryDate).Date.AddDays(wuConfigGraceDays)))
                    return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.EidExpired.ToString());
            }

            bool isEnabledMoneyTransferNonWUEIDGraceBlockTransaction = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferNonWUEIDGraceBlockTransaction);
            if (isEnabledMoneyTransferNonWUEIDGraceBlockTransaction)
            {
                int nonWUConfigGraceDays = _moneyTransferServiceSettings.NonWUEmiratesIdExpiryGracePeriodDays;
                if (user.CardHolder is null || (user.CardHolder.EmiratesIdExpiryDate.HasValue &&
                   DateTime.Now.Date >= Convert.ToDateTime(user.CardHolder.EmiratesIdExpiryDate).Date.AddDays(nonWUConfigGraceDays)))
                    return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.EidExpired.ToString());
            }
            var disableLoyaltyWaive = await _featureManager.IsEnabledAsync(FeatureFlags.Mt_DisableLoyaltyWaive);
            var freeTransferDetails = await this._unitOfWork.MoneyTransferTransactions.GetFreeTransferDetails(beneficiary.UserId,
                                                                                                           _rakSettings.LoyaltyLimitAmount,
                                                                                                           Convert.ToDateTime(_rakSettings.LoyaltyImplementDate),
                                                                                                           _rakSettings.LoyaltyLimitCount,
                                                                                                           disableLoyaltyWaive);

            var transferFees = await _lookupService.GetTransferFees(beneficiary.CountryCode, transferMethodForFees, (decimal)transferRequestDto.SendAmount.Amount, beneficiary.UserId);

            decimal? chargesAmount = 0M;
            decimal? totalCharges = 0M;

            if (transferFees != null && transferFees.Data.HasValue)
            {
                // Subtracting Rakbank's transfer fees and adding C3Pay's transfer fees to get the total debit amount
                chargesAmount = transferFees.Data;
                totalCharges = transferFees.Data;
            }

            if (freeTransferDetails.FreeFirstTransfer || freeTransferDetails.FreeLoyaltyTransfer)
            {
                // Removing transfer fees because of waiver to get the total debit amount.
                chargesAmount = 0;
                totalCharges = 0;
            }

            #region Check balance and calculate max sendable amount
            var totalAmount = (decimal)transferRequestDto.SendAmount.Amount + totalCharges;
            var tryGetBalance = await this.GetBalance(beneficiary.User);
            if (tryGetBalance.IsSuccessful == false)
                return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.InsufficientBalance.ToString());

            if (tryGetBalance.Data < totalAmount)
            {
                return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.InsufficientBalance.ToString());
            }
            #endregion


            // Limits
            if (provider != null && provider.WUSendMoneyEnabled)
            {
                //Provider Level - Max Limit in Receiver Currency
                if (!string.IsNullOrWhiteSpace(transferRequestDto.ConvertedAmount) && Convert.ToDecimal(transferRequestDto.ConvertedAmount) > 0)
                {
                    if (provider.MaxLimitPerTransactionOnReceiverCurrency.HasValue &&
                                       Convert.ToDecimal(transferRequestDto.ConvertedAmount) > provider.MaxLimitPerTransactionOnReceiverCurrency)
                    {
                        return new ServiceResponse<ReviewTransferDto>(false,
                        string.Format(Errors.MoneyTransfer.MaximumTransactionLimitExceededInReceiverCurrency.Message,
                        string.Format("{0:0.00}", provider.MaxLimitPerTransactionOnReceiverCurrency), provider.Currency));
                    }
                }

                //Provider Level - Min Limit in Receiver Currency
                if (transferRequestDto.SendAmount != null && Convert.ToDecimal(transferRequestDto.SendAmount.Amount) > 0)
                {
                    if (provider.MinLimitPerTransactionOnReceiverCurrency.HasValue && provider.MinLimitPerTransactionOnReceiverCurrency > 0 &&
                                      Convert.ToDecimal(transferRequestDto.SendAmount.Amount) < provider.MinLimitPerTransactionOnReceiverCurrency)
                    {
                        return new ServiceResponse<ReviewTransferDto>(false,
                            string.Format(Errors.MoneyTransfer.MinimumTransactionLimitExceededInReceiverCurrency.Message,
                            string.Format("{0:0.00}", provider.MinLimitPerTransactionOnReceiverCurrency), provider.Currency));
                    }
                }

                var limits = await this._unitOfWork.MoneyTransferLimits.FirstOrDefaultAsync(x => x.CountryCode == beneficiary.CountryCode, i => i.BankTransferLimit, i => i.CashPickUpLimit, i => i.WalletLimit);
                if (limits != null)
                {
                    // Validate based on transfer type.
                    if (beneficiary.TransferType == MoneyTransferType.RAKMoneyCashPayout && limits.CashPickUpLimit != null)
                    {
                        if (limits.CashPickUpLimit.MinAmount.HasValue && (decimal)transferRequestDto.SendAmount.Amount < limits.CashPickUpLimit.MinAmount)
                        {
                            return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.AmountTooLow.ToString());
                        }
                        if (limits.CashPickUpLimit.MaxAmount.HasValue && (decimal)transferRequestDto.SendAmount.Amount > limits.CashPickUpLimit.MaxAmount)
                        {
                            return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.AmountTooHigh.ToString());
                        }
                    }
                    else if (beneficiary.TransferType == MoneyTransferType.Wallet && limits.WalletLimit != null)
                    {
                        if (_moneyTransferServiceSettings.WUTransactionMinLimitValidationEnabled)
                        {
                            if (limits.WalletLimit.MinAmount.HasValue && (decimal)transferRequestDto.SendAmount.Amount < limits.WalletLimit.MinAmount)
                            {
                                return new ServiceResponse<ReviewTransferDto>(false,
                                string.Format(Errors.MoneyTransfer.MinumumTransactionLimitExceededForWalletTransferType.Message,
                                string.Format("{0:0.00}", limits.WalletLimit.MinAmount)));
                            }
                            if (limits.WalletLimit.MaxAmount.HasValue && (decimal)transferRequestDto.SendAmount.Amount > limits.WalletLimit.MaxAmount)
                            {
                                return new ServiceResponse<ReviewTransferDto>(false,
                                string.Format(Errors.MoneyTransfer.MaximumTransactionLimitExceededForWalletTransferType.Message,
                                string.Format("{0:0.00}", limits.WalletLimit.MaxAmount)));
                            }
                        }
                    }
                }
            }


            // Check monthly limit.
            var now = DateTime.Now;
            var startDate = new DateTime(now.Year, now.Month, 1);
            var endDate = startDate.AddMonths(1);

            var moneyTransferTransactionsCount = await _unitOfWork.MoneyTransferTransactions.CountAsync(record => record.MoneyTransferBeneficiary.UserId == transferRequestDto.UserId
                                                                                                                  && record.CreatedDate >= startDate
                                                                                                                  && record.CreatedDate <= endDate
                                                                                                                  && record.TransferType == TransactionType.RAKMoney
                                                                                                                  && (record.Status == Status.SUCCESSFUL || record.Status == Status.PENDING));

            var moneyTransferTransactionsAmount = await _unitOfWork.MoneyTransferTransactions.DecimalSumAsync(record => record.MoneyTransferBeneficiary.UserId == transferRequestDto.UserId
                                                                                                                        && record.CreatedDate >= startDate
                                                                                                                        && record.CreatedDate <= endDate
                                                                                                                        && record.TransferType == TransactionType.RAKMoney
                                                                                                                        && (record.Status == Status.SUCCESSFUL || record.Status == Status.PENDING),
                                                                                                              a => a.SendAmount);

            var totalMonthlyCount = moneyTransferTransactionsCount + 1;
            if (totalMonthlyCount > _moneyTransferServiceSettings.UserMonthlyTransactionCountLimit)
            {
                return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.UserNoOfTransactionsLimitReached.ToString());
            }

            var totalMonthlyAmount = moneyTransferTransactionsAmount + (decimal)transferRequestDto.SendAmount.Amount;
            if (totalMonthlyAmount > _moneyTransferServiceSettings.UserMonthlyTransactionAmountLimit)
            {
                return new ServiceResponse<ReviewTransferDto>(false, TransferStatusValidationMessage.UserMonthlyAmountLimitReached.ToString());
            }

            return new ServiceResponse<ReviewTransferDto>();
        }

        private async Task<ServiceResponse<MoneyTransferBeneficiary>> BuildBeneficiary(PostBeneficiaryRequest request)
        {
            #region Validate input
            if (request is null)
            {
                return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.InvalidRequestInput.ToString());
            }

            if (request.UserId == null)
            {
                return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.UserNotExists.ToString());
            }

            if (request.MoneyTransferMethodId <= 0)
            {
                return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.TransferMethodNotExists.ToString());
            }

            if (request.SubmittedFields is null || request.SubmittedFields.Count == 0)
            {
                return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.FieldsNotFound.ToString());
            }
            #endregion


            #region Check max count based on transfer type
            var currentCount = await _unitOfWork.MoneyTransferBeneficiaries.CountAsync(b => b.UserId == request.UserId
                                                                                            && b.IsDeleted == false
                                                                                            && b.TransferType != MoneyTransferType.DirectTransfer);

            // TODO-Raed: Update this to use the property from the new settings class.
            if (currentCount >= _rakSettings.MoneyTransferBeneficiaryCount)
            {
                return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.ExceedBeneficiaryCountLimit.ToString());
            }
            #endregion

            // Build beneficiary object.
            var beneficiary = new MoneyTransferBeneficiary()
            {
                // Default status of the beneficiary.
                Status = Status.PENDING,

                // A list which will hold any additional beneficiary details.
                BeneficiaryAdditionalFields = new List<BeneficiaryAdditionalField>()
            };

            #region User details
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Id == request.UserId
                                                                        && u.IsDeleted == false
                                                                        && u.IsBlocked == false
                                                                        && u.ApplicationId == MobileApplicationId.C3Pay,
                                                                   include => include.CardHolder);

            if (user is null) return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.UserNotExists.ToString());

            if (user.ApplicationId == MobileApplicationId.C3Pay && user.CardHolder.BelongsToExchangeHouse)
            {
                return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.ExchangeHouseUser.ToString());
            }

            // Set user details.
            beneficiary.UserId = user.Id;
            beneficiary.DocumentNumber = user.CardHolder.EmiratesId;
            beneficiary.DocumentType = "Emirates_ID";
            #endregion


            #region Transfer method details
            MoneyTransferMethod transferMethod;
            if (user.CardHolder.CorporateId == "99998")
            {
                transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.Id == request.MoneyTransferMethodId,
                                                                                                      include => include.MoneyTransferProvider);

            }
            else
            {
                transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.Id == request.MoneyTransferMethodId
                                                                                                      && x.IsActive == true,
                                                                                                      include => include.MoneyTransferProvider);

            }

            if (transferMethod is null) return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.TransferMethodNotExists.ToString());


            // Set transfer type and country code.
            switch (transferMethod.MoneyTransferProvider.MoneyTransferMethodType)
            {
                case MoneyTransferMethodType.BankTransfer: beneficiary.TransferType = MoneyTransferType.OutsideUAE; break;
                case MoneyTransferMethodType.CashPickup: beneficiary.TransferType = MoneyTransferType.RAKMoneyCashPayout; break;
                case MoneyTransferMethodType.Wallet: beneficiary.TransferType = MoneyTransferType.Wallet; break;
                default: break;
            }

            beneficiary.CountryCode = transferMethod.MoneyTransferProvider.CountryCode;
            #endregion


            #region Money transfer reason
            if (request.MoneyTransferReasonId <= 0)
            {
                // Set the default money transfer reason ID.
                request.MoneyTransferReasonId = 8;
                beneficiary.MoneyTransferReasonId = request.MoneyTransferReasonId;
            }
            else
            {
                var reason = await this._unitOfWork.MoneyTransferReasons.FirstOrDefaultAsync(x => x.Id == request.MoneyTransferReasonId);
                beneficiary.MoneyTransferReasonId = reason.Id;
            }
            #endregion


            // Work on submitted fields.
            if (request != null && request.SubmittedFields != null && request.SubmittedFields.Count > 0)
            {
                // Remove any fields with null codes or values.
                request.SubmittedFields = request.SubmittedFields.Where(x => string.IsNullOrWhiteSpace(x.FieldCode) == false
                                                                             && string.IsNullOrWhiteSpace(x.FieldValue) == false).ToList();
            }

            #region Names
            // Get name from the "FullName" type if found, else get it from "FirstName", "MiddleName" and "LastName" fields.
            var tryGetFullName = GetFieldValue(request.SubmittedFields, FieldCode.FullName);
            if (tryGetFullName.IsSuccessful == true)
            {
                var fullName = tryGetFullName.Data;
                if (fullName.Length > 50)
                {
                    return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.FullNameNotFound.ToString());
                }

                try
                {
                    var (firstName, lastName) = ProcessFullName(fullName, beneficiary.CountryCode);
                    beneficiary.FirstName = firstName;
                    beneficiary.LastName = lastName;
                }
                catch (Exception)
                {
                    var nameParts = fullName.Trim().Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    beneficiary.FirstName = nameParts[0].Length > 25 ? nameParts[0].Substring(0, 25) : nameParts[0];
                    beneficiary.LastName = string.Join(" ", nameParts.Skip(1)).Length > 50 ?
                        string.Join(" ", nameParts.Skip(1)).Substring(0, 50) :
                        string.Join(" ", nameParts.Skip(1));
                }
            }
            else
            {
                // First name.
                var tryGetFirstName = GetFieldValue(request.SubmittedFields, FieldCode.FirstName);
                if (tryGetFirstName.IsSuccessful == false)
                {
                    return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.FirstNameNotExists.ToString());
                }

                var firstName = tryGetFirstName.Data;
                if (firstName.Length > 26)
                {
                    return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.ExceedFirstNameLength.ToString());
                }

                beneficiary.FirstName = firstName;


                // Middle name.
                var tryGetMiddleName = GetFieldValue(request.SubmittedFields, FieldCode.MiddleName);
                if (tryGetMiddleName.IsSuccessful)
                {
                    var middleName = tryGetMiddleName.Data;
                    if (middleName.Length > 26)
                    {
                        return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.ExceedMiddleNameLength.ToString());
                    }
                    else
                    {
                        beneficiary.MiddleName = middleName;
                    }
                }


                // Last name.
                var tryGetLastName = GetFieldValue(request.SubmittedFields, FieldCode.LastName);
                if (tryGetLastName.IsSuccessful == false)
                {
                    return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.LastNameNotExists.ToString());
                }

                var lastName = tryGetLastName.Data;
                if (lastName.Length > 26)
                {
                    return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.ExceedLastNameLength.ToString());
                }

                beneficiary.LastName = lastName;
            }

            // Add names to additional fields.
            beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "BENE_FIRST_NAME",
                FieldValue = beneficiary.FirstName
            });
            beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "BENE_LAST_NAME",
                FieldValue = beneficiary.LastName
            });
            #endregion


            // Custom logic for each transfer type.
            switch (beneficiary.TransferType)
            {
                case MoneyTransferType.OutsideUAE:
                    var tryBuildBankTransferBeneficiary = await BuildBankTransferBeneficiary(request, beneficiary);
                    if (tryBuildBankTransferBeneficiary.IsSuccessful == false) return new ServiceResponse<MoneyTransferBeneficiary>(false, tryBuildBankTransferBeneficiary.ErrorMessage);
                    break;
                case MoneyTransferType.RAKMoneyCashPayout:
                    var tryBuildCashPickupBeneficiary = await BuildCashPickupBeneficiary(request, beneficiary);
                    if (tryBuildCashPickupBeneficiary.IsSuccessful == false) return new ServiceResponse<MoneyTransferBeneficiary>(false, tryBuildCashPickupBeneficiary.ErrorMessage);
                    break;
                case MoneyTransferType.Wallet:
                    var tryBuildWalletBeneficiary = await BuildWalletBeneficiary(request, beneficiary);
                    if (tryBuildWalletBeneficiary.IsSuccessful == false) return new ServiceResponse<MoneyTransferBeneficiary>(false, tryBuildWalletBeneficiary.ErrorMessage);
                    break;
                default:
                    break;
            }


            // Check if user is black listed.
            var isBlackListed = await _unitOfWork.BlackListedEntities.Any(b => b.EntityType == BlackListedEntityType.MoneyTransferAccountNumber
                                                                   && b.CountryCode == beneficiary.CountryCode
                                                                   && b.Identifier == beneficiary.AccountNumber
                                                                   && b.IsActive);

            if (isBlackListed)
            {
                user.Block(UserBlockType.MoneyTransferBeneficiaryBlackListed);
                await _unitOfWork.CommitAsync();
                await _identityService.LockUserAccountAsync(user.PhoneNumber);

                return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.UserBlocked.ToString());
            }

            return new ServiceResponse<MoneyTransferBeneficiary>(beneficiary);
        }
        private (string firstName, string lastName) ProcessFullName(string fullName, string countryCode)
        {
            // Remove titles like Mr., Mrs., etc from the start of the full name if followed by a space
            var titlesToRemove = new[] { "Mr.", "Mr", "Mrs.", "Mrs", "Ms.", "Ms", "Miss.", "Miss", "Dr.", "Dr" };
            var cleanFullName = fullName.TrimStart();
            foreach (var title in titlesToRemove)
            {
                if (cleanFullName.StartsWith(title, StringComparison.OrdinalIgnoreCase) &&
                    cleanFullName.Length > title.Length &&
                    char.IsWhiteSpace(cleanFullName[title.Length]))
                {
                    cleanFullName = cleanFullName.Substring(title.Length).TrimStart();
                    break;
                }
            }

            // Split into name parts
            var nameParts = cleanFullName.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

            // Handle first name - must be 3-25 chars
            var firstName = nameParts[0];
            int minLength = countryCode == "BD" ? 2 : 3; // In Bangladesh, the first name can be 2 characters long.
            if (firstName.Length < minLength && nameParts.Length > 1)
            {
                // If first name too short, include next part
                firstName = $"{firstName} {nameParts[1]}";
                // Remove the used part from name parts
                nameParts = nameParts.Skip(2).ToArray();
            }
            else
            {
                nameParts = nameParts.Skip(1).ToArray();
            }

            // Combine remaining parts for last name
            var lastName = string.Join(" ", nameParts);

            // Validate and trim lengths
            firstName = firstName.Length > 25 ? firstName.Substring(0, 25) : firstName;
            lastName = lastName.Length > 50 ? lastName.Substring(0, 50) : lastName;

            return (firstName, lastName);
        }
        private async Task<ServiceResponse<List<BeneficarySummary>>> GenerateBeneficiarySummaryScreenDetails(MoneyTransferBeneficiary beneficiary, string languageCode)
        {
            // Find all translations.
            var translationsCached = await this._cacheService.GetRecordAsync<IList<MoneyTransferTextLocalization>>(_moneyTransferTextLocalizationCacheKey + "_" + languageCode);
            if (translationsCached is null || translationsCached.Count == 0)
            {
                translationsCached = await this._unitOfWork.MoneyTransferTextLocalizations.FindAsync(x => x.LanguageCode == languageCode);
                await this._cacheService.SetRecordAsync(_moneyTransferTextLocalizationCacheKey + "_" + languageCode, translationsCached, TimeSpan.FromDays(30));
            }

            var summary = new List<BeneficarySummary>();

            var nameKey = translationsCached.FirstOrDefault(x => x.StringKey == "Name");

            // If the first name and the last name are the same, show one.
            if (beneficiary.FirstName == beneficiary.LastName)
            {
                summary.Add(new BeneficarySummary()
                {
                    Label = nameKey == null ? "Name:" : nameKey.Translation + ":",
                    Value = beneficiary.FirstName
                });
            }
            else
            {
                summary.Add(new BeneficarySummary()
                {
                    Label = nameKey == null ? "Name:" : nameKey.Translation + ":",
                    Value = beneficiary.FullName
                });
            }

            var corridor = await this._unitOfWork.RemittanceDestinations.FirstOrDefaultAsync(x => x.Code2 == beneficiary.CountryCode);
            var countryKey = translationsCached.FirstOrDefault(x => x.StringKey == "Country");

            summary.Add(new BeneficarySummary()
            {
                Label = countryKey == null ? "Country:" : countryKey.Translation + ":",
                Value = corridor.Name
            });

            if (beneficiary.TransferType == MoneyTransferType.OutsideUAE)
            {
                var IbanKey = translationsCached.FirstOrDefault(x => x.StringKey == "IBAN");

                // Account number / IBAN.
                if (corridor.Code2 == "PK" && IsValidPakistanIban(beneficiary.AccountNumber))
                {
                    summary.Add(new BeneficarySummary()
                    {
                        Label = IbanKey == null ? "IBAN:" : IbanKey.Translation + ":",
                        Value = beneficiary.AccountNumber
                    });
                }
                else
                {
                    var accountNumberkey = translationsCached.FirstOrDefault(x => x.StringKey == "Account_Number");

                    summary.Add(new BeneficarySummary()
                    {
                        Label = accountNumberkey == null ? "Account Number:" : accountNumberkey.Translation + ":",
                        Value = beneficiary.AccountNumber
                    });
                }

                var bank = await this._unitOfWork.MoneyTransferBanks.FirstOrDefaultAsync(b => b.Name == beneficiary.BankName && b.MoneyTransferPartnerId == (int)MoneyTransferPartnerType.DirectClient);

                if (corridor.Code2 == "IN" && string.IsNullOrEmpty(beneficiary.SelectedIfscCode) == false)
                {
                    var IfscCodeKey = translationsCached.FirstOrDefault(x => x.StringKey == "IFSC_Code");

                    summary.Add(new BeneficarySummary()
                    {
                        Label = IfscCodeKey == null ? "IFSC Code:" : IfscCodeKey.Translation + ":",
                        Value = beneficiary.IdentifierCode1
                    });
                }

                if (string.IsNullOrWhiteSpace(beneficiary.BankName) == false)
                {
                    var bankNameKey = translationsCached.FirstOrDefault(x => x.StringKey == "Bank_Name");

                    summary.Add(new BeneficarySummary()
                    {
                        Label = bankNameKey == null ? "Bank Name:" : bankNameKey.Translation + ":",
                        Value = beneficiary.BankName
                    });
                }

                if (beneficiary.CountryCode != "PH" && string.IsNullOrWhiteSpace(beneficiary.BankBranchName) == false && bank?.RequiresBranch == true && beneficiary.SelectedBranchId > 0)
                {
                    var branchNameKey = translationsCached.FirstOrDefault(x => x.StringKey == "Branch_Name");

                    summary.Add(new BeneficarySummary()
                    {
                        Label = branchNameKey == null ? "Branch Name:" : branchNameKey.Translation + ":",
                        Value = beneficiary.BankBranchName
                    });
                }

                // Get additional custom fields for Ghana.
                if (beneficiary.CountryCode == "GH")
                {
                    var addressLine1 = beneficiary.BeneficiaryAdditionalFields.FirstOrDefault(x => x.FieldCode == "ADDRESS_LINE1").FieldValue;
                    summary.Add(new BeneficarySummary()
                    {
                        Label = "Address Line 1:",
                        Value = addressLine1
                    });

                    var city = beneficiary.BeneficiaryAdditionalFields.FirstOrDefault(x => x.FieldCode == "CITY").FieldValue;

                    var cityKey = translationsCached.FirstOrDefault(x => x.StringKey == "City");

                    summary.Add(new BeneficarySummary()
                    {
                        Label = cityKey == null ? "City:" : cityKey.Translation + ":",
                        Value = city
                    });
                }

                // Add additional custom fields for Ethiopia.
                if (beneficiary.CountryCode == "ET")
                {
                    var postalCode = beneficiary.BeneficiaryAdditionalFields.FirstOrDefault(x => x.FieldCode == "POSTAL_CODE").FieldValue;
                    summary.Add(new BeneficarySummary()
                    {
                        Label = "Postal Code:",
                        Value = postalCode
                    });
                }
            }
            else if (beneficiary.TransferType == MoneyTransferType.RAKMoneyCashPayout)
            {
                var transactionTypeKey = translationsCached.FirstOrDefault(x => x.StringKey == "Transaction_Type");

                summary.Add(new BeneficarySummary()
                {
                    Label = transactionTypeKey == null ? "Transaction Type:" : transactionTypeKey.Translation + ":",
                    Value = "Cash Pickup"
                });
            }
            else if (beneficiary.TransferType == MoneyTransferType.Wallet)
            {
                var mobileNumberKey = translationsCached.FirstOrDefault(x => x.StringKey == "MobileNumber");

                summary.Add(new BeneficarySummary()
                {
                    Label = mobileNumberKey == null ? "Mobile Number:" : mobileNumberKey.Translation + ":",
                    Value = corridor.IsdCode + beneficiary.PhoneNumber
                });

                var providerCode = beneficiary.BeneficiaryAdditionalFields.FirstOrDefault(x => x.FieldCode == "SERVICE_PROVIDER").FieldValue;
                var provider = await this._unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.MoneyTransferMethodType == MoneyTransferMethodType.Wallet && x.ProviderCode == providerCode);

                summary.Add(new BeneficarySummary()
                {
                    Label = "Wallet Provider:",
                    Value = provider.Name
                });
            }

            // Find transfer reason.
            var reason = await this._unitOfWork.MoneyTransferReasons.FirstOrDefaultAsync(x => x.Id == beneficiary.MoneyTransferReasonId);
            if (reason != null)
            {
                var purposeKey = translationsCached.FirstOrDefault(x => x.StringKey == "Purpose");

                summary.Add(new BeneficarySummary()
                {
                    Label = purposeKey == null ? "Purpose:" : purposeKey.Translation + ":",
                    Value = reason.Reason
                });
            }

            return new ServiceResponse<List<BeneficarySummary>>(summary);
        }

        private async Task<ServiceResponse> BuildBankTransferBeneficiary(PostBeneficiaryRequest request, MoneyTransferBeneficiary beneficiary)
        {
            // Account number / IBAN.
            var accountNumberField = request.SubmittedFields.FirstOrDefault(x => x.FieldCode == FieldCode.AccountNumber.ToString()
                                                                                 || x.FieldCode == FieldCode.IBAN.ToString()
                                                                                 || x.FieldCode == FieldCode.AccountNumberOrIBAN.ToString());
            if (accountNumberField is null)
            {
                return new ServiceResponse<ReviewBeneficiaryDto>(false, TransferStatusValidationMessage.AccountNumberNotExists.ToString());
            }

            var accountNumber = accountNumberField.FieldValue;
            if (string.IsNullOrWhiteSpace(accountNumber))
            {
                return new ServiceResponse<ReviewBeneficiaryDto>(false, TransferStatusValidationMessage.AccountNumberNotExists.ToString());
            }

            beneficiary.AccountNumber = accountNumber.Trim();

            // Remove spaces from account number.
            beneficiary.AccountNumber = Regex.Replace(beneficiary.AccountNumber, @"\s+", "");

            // For Pakistan, we need to know if the provided account number is an IBAN or not.
            if (beneficiary.CountryCode == "PK")
            {
                var isValidIban = IsValidPakistanIban(beneficiary.AccountNumber);
                if (isValidIban)
                {
                    beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
                    {
                        FieldCode = "IBAN",
                        FieldValue = accountNumber
                    });
                }
                else
                {
                    beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
                    {
                        FieldCode = "ACCOUNT_NUMBER",
                        FieldValue = accountNumber
                    });
                }
            }
            else
            {
                // Add account number for any other country.
                beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
                {
                    FieldCode = "ACCOUNT_NUMBER",
                    FieldValue = accountNumber
                });
            }

            // Check for duplicate bank transfer beneficiaries.
            var foundDuplicate = await _unitOfWork.MoneyTransferBeneficiaries.FindAsync(b => b.IsDeleted == false
                                                                                             && b.UserId == request.UserId
                                                                                             && b.TransferType == MoneyTransferType.OutsideUAE
                                                                                             && b.AccountNumber == beneficiary.AccountNumber);

            if (foundDuplicate.Any())
            {
                ExistingBeneficiaryId = foundDuplicate.First().Id;
                return new ServiceResponse<ReviewBeneficiaryDto>(false, TransferStatusValidationMessage.BeneficiaryAlreadyExists.ToString());
            }

            // Bank ID will always be sent.
            var tryGetBankId = GetFieldValue(request.SubmittedFields, FieldCode.BankId);
            if (tryGetBankId.IsSuccessful == false)
            {
                return new ServiceResponse(false, TransferStatusValidationMessage.BankCodeNotExists.ToString());
            }

            MoneyTransferBank bank = null;
            MoneyTransferBranch branch = null;
            if (int.TryParse(tryGetBankId?.Data, out int bankId))
            {
                bank = await _unitOfWork.MoneyTransferBanks.FirstOrDefaultAsync(x => x.Id == bankId);

                // In case the bank does not require a branch selection from the FE, find the last successful transfer made to the selected bank and use that branch.
                if (bank.RequiresBranch == false)
                {
                    // Skip this step for any WU bank transfer beneficiaries.
                    var wuCorridros = new List<string> { "MA", "NG", "UG", "KE", "ET", "GH", "EG", "ID" };
                    if (wuCorridros.Contains(beneficiary.CountryCode) == false)
                    {
                        var lastSuccessfulTransaction = await _unitOfWork.MoneyTransferTransactions.GetBankLastSuccessfulTransaction(bank.Name,
                                                                                                                                     beneficiary.CountryCode,
                                                                                                                                     TransactionType.RAKMoney);
                        if (lastSuccessfulTransaction != null && lastSuccessfulTransaction.MoneyTransferBeneficiary != null)
                        {
                            beneficiary.IdentifierCode1 = lastSuccessfulTransaction.MoneyTransferBeneficiary.IdentifierCode1;
                            beneficiary.IdentifierCode2 = lastSuccessfulTransaction.MoneyTransferBeneficiary.IdentifierCode2;

                            beneficiary.BankName = lastSuccessfulTransaction.MoneyTransferBeneficiary.BankName;
                            beneficiary.BankBranchName = lastSuccessfulTransaction.MoneyTransferBeneficiary.BankBranchName;
                        }
                    }
                }
            }

            // Try to find branch.
            // Priority will be:
            // 1- IFSC Code.
            // 2- Branch ID.
            var tryGetIfscCode = GetFieldValue(request.SubmittedFields, FieldCode.IFSC);
            if (tryGetIfscCode.IsSuccessful == true)
            {
                var ifscCode = tryGetIfscCode.Data;
                branch = await this._unitOfWork.MoneyTransferBranches.FirstOrDefaultAsync(b => b.PrimaryIdentifierCode == ifscCode
                                                                                               && b.Bank.MoneyTransferPartnerId == (int)MoneyTransferPartnerType.DirectClient,
                                                                                               include => include.Bank);

                beneficiary.IdentifierCode1 = branch.PrimaryIdentifierCode;
                beneficiary.IdentifierCode2 = branch.SecondaryIdentifierCode;

                beneficiary.BankName = bank.Name;
                beneficiary.BankBranchName = branch.Name;
                beneficiary.SelectedIfscCode = ifscCode;
            }

            if (branch is null)
            {
                var tryGetBranchId = GetFieldValue(request.SubmittedFields, FieldCode.BranchId);
                if (tryGetBranchId.IsSuccessful == true)
                {
                    if (int.TryParse(tryGetBranchId.Data, out int branchId))
                    {
                        branch = await this._unitOfWork.MoneyTransferBranches.FirstOrDefaultAsync(x => x.Id == branchId);

                        beneficiary.IdentifierCode1 = branch.PrimaryIdentifierCode;
                        beneficiary.IdentifierCode2 = branch.SecondaryIdentifierCode;

                        beneficiary.BankName = bank.Name;
                        beneficiary.BankBranchName = branch.Name;
                        beneficiary.SelectedBranchId = branchId;
                    }
                }
            }

            if (branch is null)
            {
                if (beneficiary.CountryCode == "PH")
                {
                    branch = await this._unitOfWork.MoneyTransferBranches.FirstOrDefaultAsync(b => b.BankId == bank.Id);

                    beneficiary.IdentifierCode1 = branch.PrimaryIdentifierCode;
                    beneficiary.IdentifierCode2 = branch.SecondaryIdentifierCode;

                    beneficiary.BankName = bank.Name;
                    beneficiary.BankBranchName = branch.Name;
                }
            }

            // Final checks.
            if (string.IsNullOrWhiteSpace(beneficiary.IdentifierCode1))
            {
                if (bank.RequiresBranch == false && string.IsNullOrWhiteSpace(bank.PrimaryIdentifierCode) == false)
                {
                    beneficiary.IdentifierCode1 = bank.PrimaryIdentifierCode;
                    beneficiary.BankName = bank.Name;
                }
                else
                {
                    return new ServiceResponse<ReviewBeneficiaryDto>(false, TransferStatusValidationMessage.BankNotExists.ToString());
                }
            }

            beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "BANK_NAME",
                FieldValue = beneficiary.BankName
            });

            if (string.IsNullOrWhiteSpace(beneficiary.BankBranchName) == false)
            {
                beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
                {
                    FieldCode = "BRANCH_NAME",
                    FieldValue = beneficiary.BankBranchName
                });
            }


            // Add additional custom fields for Ghana.
            if (beneficiary.CountryCode == "GH")
            {
                var tryGetAddressLine1 = GetFieldValue(request.SubmittedFields, FieldCode.AddressLine1);
                if (tryGetAddressLine1.IsSuccessful == false)
                {
                    return new ServiceResponse(false, TransferStatusValidationMessage.AddressLine1NotFound.ToString());
                }

                beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
                {
                    FieldCode = "ADDRESS_LINE1",
                    FieldValue = tryGetAddressLine1.Data
                });

                var tryGetCity = GetFieldValue(request.SubmittedFields, FieldCode.City);
                if (tryGetCity.IsSuccessful == false)
                {
                    return new ServiceResponse(false, TransferStatusValidationMessage.CityNotFound.ToString());
                }

                beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
                {
                    FieldCode = "CITY",
                    FieldValue = tryGetCity.Data
                });
            }

            // Add additional custom fields for Ethiopia.
            if (beneficiary.CountryCode == "ET")
            {
                var tryGetPostalCode = GetFieldValue(request.SubmittedFields, FieldCode.PostalCode);
                if (tryGetPostalCode.IsSuccessful == false)
                {
                    return new ServiceResponse(false, TransferStatusValidationMessage.PostalCodeNotFound.ToString());
                }

                beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
                {
                    FieldCode = "POSTAL_CODE",
                    FieldValue = tryGetPostalCode.Data
                });
            }

            return new ServiceResponse();
        }

        private async Task<ServiceResponse> BuildCashPickupBeneficiary(PostBeneficiaryRequest request, MoneyTransferBeneficiary beneficiary)
        {
            // Check for duplicate cash pickup beneficiaries.
            var foundDuplicate = await _unitOfWork.MoneyTransferBeneficiaries.FindAsync(b => b.IsDeleted == false
                                                                                             && b.UserId == request.UserId
                                                                                             && b.FirstName == beneficiary.FirstName
                                                                                             && b.LastName == beneficiary.LastName
                                                                                             && b.TransferType == MoneyTransferType.RAKMoneyCashPayout);

            if (foundDuplicate.Any())
            {
                ExistingBeneficiaryId = foundDuplicate.First().Id;
                return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.BeneficiaryAlreadyExists.ToString());
            }

            beneficiary.IsBranchSelected = false;

            return new ServiceResponse();
        }

        private async Task<ServiceResponse> BuildWalletBeneficiary(PostBeneficiaryRequest request, MoneyTransferBeneficiary beneficiary)
        {
            // Phone number.
            var tryGetPhoneNumber = GetFieldValue(request.SubmittedFields, FieldCode.PhoneNumber);
            if (tryGetPhoneNumber.IsSuccessful == false)
            {
                return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.PhoneNumberIsRequired.ToString());
            }

            var phoneNumber = tryGetPhoneNumber.Data;

            beneficiary.PhoneNumber = phoneNumber;
            if (beneficiary.PhoneNumber[0] == '0')
            {
                // Remove the first character
                beneficiary.PhoneNumber = beneficiary.PhoneNumber.Substring(1);
            }

            //#23044 - WU Allowing Indonesia phone number 0 prefix
            bool us23044 = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferUS23044);
            if (us23044)
            {
                if (!string.IsNullOrWhiteSpace(beneficiary.CountryCode) && beneficiary.CountryCode.ToUpper() == "ID")
                {
                    if (beneficiary.PhoneNumber[0] != '0')
                        beneficiary.PhoneNumber = $"0{beneficiary.PhoneNumber}";
                }
            }
            // Check for duplicate wallet beneficiaries.
            var foundDuplicate = await _unitOfWork.MoneyTransferBeneficiaries.FindAsync(b => b.IsDeleted == false
                                                                                             && b.UserId == request.UserId
                                                                                             && b.PhoneNumber == beneficiary.PhoneNumber
                                                                                             && b.TransferType == MoneyTransferType.Wallet);

            if (foundDuplicate.Any())
            {
                ExistingBeneficiaryId = foundDuplicate.First().Id;
                return new ServiceResponse<MoneyTransferBeneficiary>(false, TransferStatusValidationMessage.BeneficiaryAlreadyExists.ToString());
            }


            var transferMethod = await this._unitOfWork.MoneyTransferMethods.FirstOrDefaultAsync(x => x.Id == request.MoneyTransferMethodId,
                                                                                                      include => include.MoneyTransferProvider);

            beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "SERVICE_PROVIDER",
                FieldValue = transferMethod.MoneyTransferProvider.ProviderCode
            });

            var corridor = await this._unitOfWork.RemittanceDestinations.FirstOrDefaultAsync(x => x.Code2 == beneficiary.CountryCode);

            beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "COUNTRY_CODE",
                FieldValue = corridor.IsdCode.Replace("+", "")
            });

            beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
            {
                FieldCode = "MOBILE_NO",
                FieldValue = beneficiary.PhoneNumber
            });

            //TODO-Raed: Move these default values to settings object.
            if (corridor.Code2 == "IN")
            {
                beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
                {
                    FieldCode = "ADDRESS_LINE1",
                    FieldValue = "CM 1102 LAS LAJAS"
                });
                beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
                {
                    FieldCode = "CITY",
                    FieldValue = "Pune"
                });
                beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
                {
                    FieldCode = "STATE",
                    FieldValue = "MAHARASHTRA"
                });
                beneficiary.BeneficiaryAdditionalFields.Add(new BeneficiaryAdditionalField()
                {
                    FieldCode = "POSTAL_CODE",
                    FieldValue = "455551"
                });
            }

            return new ServiceResponse();
        }

        private ServiceResponse<string> GetFieldValue(List<SubmittedField> submittedFields, FieldCode fieldCode)
        {
            var field = submittedFields.FirstOrDefault(x => x.FieldCode.ToLower() == fieldCode.ToString().ToLower());

            if (field is null)
            {
                return new ServiceResponse<string>(false, "NotFound");
            }

            var fieldValue = field.FieldValue?.Trim();

            if (string.IsNullOrWhiteSpace(fieldValue))
            {
                return new ServiceResponse<string>(false, "NotFound");
            }

            return new ServiceResponse<string>(fieldValue);
        }

        private bool IsValidPakistanIban(string accountNumber)
        {
            // Pakistan IBAN should start with PK.
            if (accountNumber.StartsWith("PK", StringComparison.OrdinalIgnoreCase) == false) return false;

            // Pakistan IBAN should have a length of 24.
            if (accountNumber?.Length != 24) return false;

            for (int i = 0; i < accountNumber.Length; i++)
            {
                var @char = accountNumber[i];
                if (i == 0 && @char != 'P') return false;
                if (i == 1 && @char != 'K') return false;

                if (i == 2 && char.IsDigit(@char) == false) return false;
                if (i == 3 && char.IsDigit(@char) == false) return false;

                if (i > 3 && i <= 7 && char.IsLetter(@char) == false) return false;
            }

            return true;
        }

        public async Task<ServiceResponse<FetchRateDto>> FetchRate(string amount, string targetCurrency, string countryCode, string transferMethod)
        {
            var result = new FetchRateDto();
            var decimalAmount = Convert.ToDecimal(amount);

            transferMethod = transferMethod.ToLower();

            MoneyTransferProvider corridor = null;

            // Cast enum since we have two enum types with the same name.
            if (transferMethod == MoneyTransferMethodType.BankTransfer.ToString().ToLower())
            {
                corridor = await _unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.CountryCode == countryCode
                && x.Currency == targetCurrency
                && x.MoneyTransferMethodType == MoneyTransferMethodType.BankTransfer);
            }
            else if (transferMethod == MoneyTransferMethodType.CashPickup.ToString().ToLower())
            {
                corridor = await _unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.CountryCode == countryCode
                && x.Currency == targetCurrency
                && x.MoneyTransferMethodType == MoneyTransferMethodType.CashPickup);
            }
            else if (transferMethod == MoneyTransferMethodType.Wallet.ToString().ToLower())
            {
                corridor = await _unitOfWork.MoneyTransferProviders.FirstOrDefaultAsync(x => x.CountryCode == countryCode
                && x.Currency == targetCurrency
                && x.MoneyTransferMethodType == MoneyTransferMethodType.Wallet);
            }

            if (corridor is null)
            {
                return new ServiceResponse<FetchRateDto>(false, "CorridorNotSupported");
            }

            if (corridor.WUGetRatesEnabled)
            {
                // Get rate from new service.
                var request = new GetFxRateRequest()
                {
                    EmiratesId = this._moneyTransferServiceSettings.RefreshRatesEmiratesId,
                    BaseCurrency = ConstantParam.DefaultCurrency,
                    TargetCurrency = targetCurrency,

                    ExchangeAmount = new ExchangeAmount()
                    {
                        Amount = decimalAmount.ToString(),
                        Currency = ConstantParam.DefaultCurrency
                    },

                    BankCountryCode = countryCode,
                    CalculateCharges = true
                };

                // Cast enum since we have two enum types with the same name.
                if (transferMethod == MoneyTransferMethodType.BankTransfer.ToString().ToLower())
                {
                    request.MoneyTransferMethodType = Enums.MoneyTransferMethodType.BankTransfer;
                }
                else if (transferMethod == MoneyTransferMethodType.CashPickup.ToString().ToLower())
                {
                    request.MoneyTransferMethodType = Enums.MoneyTransferMethodType.CashPickup;
                }
                else if (transferMethod == MoneyTransferMethodType.Wallet.ToString().ToLower())
                {
                    request.MoneyTransferMethodType = Enums.MoneyTransferMethodType.Wallet;
                }

                var tryGetFxRate = await _externalProviderMoneyTransferService.GetFxRate(request);
                if (tryGetFxRate.IsSuccessful == false)
                {
                    return new ServiceResponse<FetchRateDto>(false, "NoRateFound");
                }

                var rateResult = tryGetFxRate.Data.FxRateConversions.First();

                result.BaseCurrency = "AED";
                result.TargetCurrency = targetCurrency;

                result.FromAmount = decimalAmount.ToString();
                result.ConvertedAmount = rateResult.ConvertedAmount;

                result.Rate = Math.Round(1 / Convert.ToDecimal(rateResult.Rate), 7).ToString();
                result.Fee = tryGetFxRate.Data.FxRateConversionCharge.TotalCharges;
            }
            else
            {
                if (transferMethod == MoneyTransferMethodType.Wallet.ToString().ToLower())
                {
                    return new ServiceResponse<FetchRateDto>(false, "CorridorNotSupported");
                }

                var request = new FxRateRequestRakModel
                {
                    TransactionType = TransactionType.RAKMoney.ToString(),
                    FromCurrency = ConstantParam.DefaultCurrency,
                    ToCurrency = targetCurrency,
                    Fxvalue = new FxValueRakModel()
                    {
                        Amount = decimalAmount.ToString(),
                        Currency = ConstantParam.DefaultCurrency
                    },
                    BankCountry = countryCode,
                    BeneficiaryId = null,
                    Charges = true
                };


                // Add this corridor to the tracking list.
                if (transferMethod == MoneyTransferMethodType.BankTransfer.ToString().ToLower())
                {
                    request.TransferType = MoneyTransferType.OutsideUAE.ToString();
                }
                if (transferMethod == MoneyTransferMethodType.CashPickup.ToString().ToLower())
                {
                    request.TransferType = MoneyTransferType.RAKMoneyCashPayout.ToString();
                }

                var tryGetFxRate = await _rakService.GetFxRate(new List<FxRateRequestRakModel>() { request }, this._moneyTransferServiceSettings.RefreshRatesEmiratesId);
                if (tryGetFxRate.IsSuccessful == false)
                {
                    return new ServiceResponse<FetchRateDto>(false, "NoRateFound");
                }

                result.BaseCurrency = "AED";
                result.TargetCurrency = targetCurrency;

                result.FromAmount = decimalAmount.ToString();
                result.ConvertedAmount = tryGetFxRate.Data.First().FxConversionRates.First().ConvertedAmount;
                result.Fee = tryGetFxRate.Data.First().ConversionCharges.TotalCharges;

                result.Rate = Math.Round(1 / Convert.ToDecimal(tryGetFxRate.Data.First().FxConversionRates.First().Rate), 7).ToString();
            }

            return new ServiceResponse<FetchRateDto>(result);
        }

        public async Task<ServiceResponse> SendFreeTransferExpiryNotification()
        {
            var todaysDate = DateTime.Now.Date;
            var details = await _unitOfWork.MoneyTransferTransactions.GetValidFreeTransferExpiryUsersDetails();

            foreach (var item in details)
            {
                var daysLeft = (item.FreeTransferExpiryDate.Date - todaysDate).TotalDays;
                switch (daysLeft)
                {
                    case 7:
                    case 4:
                    case 2:
                    case 1:
                        await _textMessageSenderService.SendFreeExpiryNotification(item.PhoneNumber, daysLeft);
                        break;
                    default:
                        break;
                }
            }

            return new ServiceResponse();
        }


        private async Task<ServiceResponse<SendMoneyTransferResultDto>> SendInstantInternationalTransfer(MoneyTransferTransaction transfer, bool isSMVEnabled)
        {
            #region Get beneficiary
            var beneficiary = await this._unitOfWork.MoneyTransferBeneficiaries.FirstOrDefaultAsync(b => b.Id == transfer.MoneyTransferBeneficiaryId
                                                                                                         && b.IsDeleted == false,
                                                                                                         i => i.User.CardHolder,
                                                                                                         i => i.ExternalBeneficiary,
                                                                                                         i => i.Country,
                                                                                                         i => i.User.UserSegmentation);
            if (beneficiary is null)
            {
                return new ServiceResponse<SendMoneyTransferResultDto>(false, TransferStatusValidationMessage.BeneficiaryNotExists.ToString());
            }
            #endregion

            #region Validate card details.
            var user = beneficiary.User;
            var cardNumber = user.CardHolder.CardNumber;
            var cardSerialNumber = user.CardHolder.CardSerialNumber;
            if (string.IsNullOrEmpty(cardNumber) || string.IsNullOrEmpty(cardSerialNumber))
            {
                return new ServiceResponse<SendMoneyTransferResultDto>(false, TransferStatusValidationMessage.InvalidCardNumber.ToString());
            }
            #endregion


            #region Validate EID Expiry

            int wuConfigGraceDays = _moneyTransferServiceSettings.WUEmiratesIdExpiryGracePeriodDays;
            if (user.CardHolder is null || (user.CardHolder.EmiratesIdExpiryDate.HasValue &&
                DateTime.Now.Date >= Convert.ToDateTime(user.CardHolder.EmiratesIdExpiryDate).Date.AddDays(wuConfigGraceDays)))
                return new ServiceResponse<SendMoneyTransferResultDto>(false, TransferStatusValidationMessage.EidExpired.ToString());

            #endregion

            #region Set transfer details.
            // Set default transfer values.
            transfer.Status = Status.PENDING;
            transfer.TransferType = TransactionType.RAKMoney;

            // Set transfer properties from beneficiary.
            transfer.ReceiveCurrency = beneficiary.Country.Currency;
            transfer.MoneyTransferReasonId = beneficiary.MoneyTransferReasonId;
            transfer.UserId = beneficiary.User.Id;
            #endregion

            TransferMethod transferMethodForFees = TransferMethod.BANKTRANSFER;

            switch (beneficiary.TransferType)
            {
                case MoneyTransferType.OutsideUAE:
                    transferMethodForFees = TransferMethod.BANKTRANSFER;
                    break;
                case MoneyTransferType.RAKMoneyCashPayout:
                    transferMethodForFees = TransferMethod.CASHPICKUP;
                    break;
                case MoneyTransferType.Wallet:
                    transferMethodForFees = TransferMethod.WALLET;
                    break;
                default:
                    break;
            }
            var disableLoyaltyWaive = await _featureManager.IsEnabledAsync(FeatureFlags.Mt_DisableLoyaltyWaive);
            var freeTransferDetails = await this._unitOfWork.MoneyTransferTransactions.GetFreeTransferDetails(beneficiary.UserId,
                                                                                                           _rakSettings.LoyaltyLimitAmount,
                                                                                                           Convert.ToDateTime(_rakSettings.LoyaltyImplementDate),
                                                                                                           _rakSettings.LoyaltyLimitCount,
                                                                                                           disableLoyaltyWaive);

            var transferFees = await _lookupService.GetTransferFees(beneficiary.CountryCode, transferMethodForFees, transfer.SendAmount, beneficiary.UserId);
            if (transferFees.IsSuccessful == false)
            {
                return new ServiceResponse<SendMoneyTransferResultDto>(false, TransferStatusValidationMessage.TransferError.ToString());
            }

            #region Referral code
            var referralCode = transfer.ReferralCode;
            var userReferralCode = beneficiary.User.ReferralCode;

            if (referralCode == userReferralCode && string.IsNullOrEmpty(userReferralCode) == false)
            {
                transfer.ReferralCode = null;
            }
            else
            {
                bool referralUserExists = false;

                if (string.IsNullOrEmpty(referralCode) == false)
                {
                    referralUserExists = await this._unitOfWork.Users.Any(u => u.Id != beneficiary.User.Id
                                                                               && u.ReferralCode == referralCode);
                }

                if (referralUserExists)
                {
                    // Referred by user.
                    if (freeTransferDetails.FreeFirstTransfer == false || transfer.SendAmount < this._referralProgramServiceSettings.MoneyTransferAmountThreshold)
                    {
                        transfer.ReferralCode = null;
                    }
                }
                else
                {
                    // Referred by entering the code.
                    if (freeTransferDetails.FreeFirstTransfer == false)
                    {
                        var referrerCodeDetails = await this._unitOfWork.MoneyTransferTransactions.GetReferrerCodeDetails(beneficiary.User.Id,
                                                                                                                          transfer.CreatedDate);
                        if (referrerCodeDetails.Eligible)
                        {
                            transfer.ReferralCode = referrerCodeDetails.ReferrerCode;
                        }
                    }
                }
            }
            #endregion

            if (transferFees != null && transferFees.Data.HasValue)
            {
                // Subtracting Rakbank's transfer fees and adding C3Pay's transfer fees to get the total debit amount
                transfer.ChargesAmount = transferFees.Data;
                transfer.TotalCharges = transferFees.Data;
            }

            if (freeTransferDetails.FreeFirstTransfer || freeTransferDetails.FreeLoyaltyTransfer)
            {
                if (freeTransferDetails.FreeFirstTransfer)
                {
                    // No fee for the first transaction.
                    transfer.WaiveType = WaiveType.FirstTransaction;
                }
                else if (freeTransferDetails.FreeLoyaltyTransfer)
                {
                    // No fee for the transaction if the user has loyalty offer.  
                    transfer.WaiveType = WaiveType.Loyalty;
                }

                // Remove charges.
                transfer.WaivedCharge = transfer.ChargesAmount;
                transfer.ChargesAmount = 0;
                transfer.TotalCharges = 0;
            }

            #region Check balance
            var totalAmount = transfer.SendAmount + transfer.TotalCharges;
            var tryGetBalance = await this.GetBalance(beneficiary.User);
            if (tryGetBalance.IsSuccessful == false || tryGetBalance.Data < totalAmount)
            {
                return new ServiceResponse<SendMoneyTransferResultDto>(false, TransferStatusValidationMessage.InsufficientBalance.ToString());
            }
            #endregion


            await SetMoneyTransferStatusAsStarted(transfer, beneficiary);

            await _unitOfWork.MoneyTransferTransactions.AddAsync(transfer);
            await _unitOfWork.CommitAsync();

            var response = new SendMoneyTransferResultDto();


            transfer = await _unitOfWork.MoneyTransferTransactions.FirstOrDefaultAsync(x => x.Id == transfer.Id,
                                                                           i => i.MoneyTransferBeneficiary.ExternalBeneficiary,
                                                                           i => i.MoneyTransferBeneficiary.Country,
                                                                           i => i.MoneyTransferStatusSteps,
                                                                           i => i.User.CardHolder,
                                                                           i => i.ExternalTransaction,
                                                                           i => i.Transaction);
            try
            {
                // New code.
                var referencePrefix = BaseEnums.TransactionPrefix.RMT.ToString();
                var referenceDigits = 12;
                var referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
                while (await this._unitOfWork.Transactions.Any(t => t.ReferenceNumber == referenceNumber))
                {
                    referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
                }


                var messageId = Guid.NewGuid().ToString();
                var placeOfBirth = await GetPlaceOfBirthAsync(transfer.User.CardHolder, transfer.MoneyTransferBeneficiary.CountryCode);
                string emiratesId = transfer.MoneyTransferBeneficiary.DocumentNumber;
                var wuTransferRequest = new CreateTransferRequest()
                {
                    TransactionType = transfer.TransferType.ToString(),
                    PurposeCode = transfer.MoneyTransferReasonId.ToString(),
                    Sender = new MoneyTransferSender()
                    {
                        CustomerIdType = transfer.MoneyTransferBeneficiary.DocumentType,
                        CustomerIdNumber = emiratesId,
                        PlaceOfBirth = placeOfBirth,
                        Nationality = await GetTwoLetterNationalityCodeAsync(transfer.User.CardHolder.Nationality, transfer.MoneyTransferBeneficiary.CountryCode),
                        DateOfBirth = await GetDateOfBirthAsync(transfer.User.CardHolder, transfer.MoneyTransferBeneficiary.CountryCode)
                    },
                    Receiver = new MoneyTransferReceiver()
                    {
                        BeneficiaryId = transfer.MoneyTransferBeneficiary.ExternalBeneficiary.ExternalId
                    },
                    Amount = new MoneyTransferAmount()
                    {
                        Currency = transfer.SendCurrency,
                        Amount = transfer.SendAmount.ToString(),
                    },
                    ApiVersion = "2.4.0",
                    C3ReferenceNumber = referenceNumber,
                    MessageId = messageId,
                    PreValidateFlag = false
                };
                _logger.LogInformation($"{transfer.ReferenceNumber} Request: {JsonConvert.SerializeObject(wuTransferRequest)}");
                transfer.TransactionPlaceOfBirth = string.IsNullOrWhiteSpace(placeOfBirth) ? string.Empty : placeOfBirth;

                ServiceResponse<Transfer> wuTransferResult = null;
                bool hasApiCallFailed = false;

                // Decide to call SMV or not
                string smvValidationMessageId = null;
                string smvValidationRemarks = null;
                string tempHostTxnId = null;
                if (isSMVEnabled)
                {
                    smvValidationMessageId = Guid.NewGuid().ToString();
                    wuTransferRequest.MessageId = smvValidationMessageId;
                    wuTransferRequest.PreValidateFlag = true;
                    // Validate with RAK 

                    try
                    {
                        var validateTransferResult = await this._externalProviderMoneyTransferService.SendMoneyTransfer(wuTransferRequest);
                        if (validateTransferResult.IsSuccessful == false)
                            smvValidationRemarks = validateTransferResult.ErrorMessage;
                        else
                            tempHostTxnId = validateTransferResult.Data.TempHostTxnId;
                    }
                    catch (Exception ex)
                    {
                        tempHostTxnId = null;
                        smvValidationRemarks = "Exception - Validate Transfer";
                        _logger.LogError($"Error while trying to validate the transfer with RAK. Error: {ex?.Message}");
                    }
                }

                var fee = transfer.TotalCharges ?? 0;
                var actualDebitAmount = (transfer.SendAmount + fee) * 100;
                var tryDebitInternationalTransfer = await DebitInternationalTransfer(transfer, actualDebitAmount, referenceNumber);
                if (tryDebitInternationalTransfer.IsSuccessful == false)
                    return new ServiceResponse<SendMoneyTransferResultDto>(false, TransferStatusValidationMessage.ErrorDebitingCard.ToString());
                try
                {
                    wuTransferRequest.PreValidateFlag = false;
                    wuTransferRequest.MessageId = messageId;
                    wuTransferRequest.TempHostTxnId = tempHostTxnId;
                    wuTransferRequest.Sender.CustomerIdNumber = emiratesId;
                    wuTransferResult = await this._externalProviderMoneyTransferService.SendMoneyTransfer(wuTransferRequest);
                }
                catch (Exception exception)
                {
                    hasApiCallFailed = true;
                    transfer.WUMsgId = messageId;

                    if (reversalsExceptionMessages.Any(exception.Message.StartsWith))
                    {
                        transfer.Status = Status.PENDINGREVERSE;
                        SetMoneyTransferStatusAsPendingReverse(transfer, exception.ToString());
                    }
                    else
                    {
                        transfer.Status = Status.FAILED;
                        await SetMoneyTransferStatusAsFailedAsync(transfer, exception.ToString());
                    }

                    transfer.Remarks = string.Join(" : ", "Exception", exception.Message);
                    transfer.UpdatedDate = DateTime.Now;

                    await _unitOfWork.CommitAsync();
                }

                if (wuTransferResult.IsSuccessful == false || (wuTransferResult.Data != null && wuTransferResult.Data.Status != ExternalStatus.P.ToString()))
                {
                    hasApiCallFailed = true;

                    // Check if error is a auto-reversal status.
                    if (reversalsErrorMessages.Any(wuTransferResult.ErrorMessage.StartsWith))
                    {
                        transfer.WUMsgId = messageId;
                        transfer.SMVValidationMessageId = string.IsNullOrWhiteSpace(smvValidationMessageId) ? string.Empty : smvValidationMessageId;
                        transfer.SMVValidationRemarks = string.IsNullOrWhiteSpace(smvValidationRemarks) ? string.Empty : smvValidationRemarks;
                        transfer.TempHostTxnId = string.IsNullOrWhiteSpace(tempHostTxnId) ? string.Empty : tempHostTxnId;
                        _logger.LogWarning($"WU:: Due to {wuTransferResult.ErrorMessage}, Transaction to RAK is marked for reversal for {transfer.ReferenceNumber}" +
                            $" and the beneficiary name :{transfer.MoneyTransferBeneficiary.FirstName} {transfer.MoneyTransferBeneficiary.LastName} ");

                        transfer.Status = Status.PENDINGREVERSE;
                        transfer.Remarks = wuTransferResult.ErrorMessage;
                        transfer.UpdatedDate = DateTime.Now;

                        // Add an entry to money transfer status progress.
                        transfer.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                        // Hide any failed steps.
                        var failedSteps = transfer.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                        foreach (var failedStep in failedSteps)
                        {
                            failedStep.Hide = true;
                        }

                        transfer.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                        {
                            Status = Status.PENDINGREVERSE,
                            Log = $"Transaction failed because of a RAK SendMoney API error.",
                            ProviderRemarks = wuTransferResult.ErrorMessage,
                            Message = ConstantParam.UM_TransferFailedButWillBeReversed
                        });

                        //Call failed event.
                        await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                        {
                            new Tuple<string, MoneyTransferEvent, Status>(transfer.User.CardHolderId, new MoneyTransferEvent()
                            {
                                Amount = transfer.SendAmount,
                                BeneficiaryName = transfer.MoneyTransferBeneficiary.FirstName,
                                Country = transfer.MoneyTransferBeneficiary.Country.Code3,
                                ErrorMessage = $"Transaction failed because of a RAK SendMoney API error. Error: {wuTransferResult.ErrorMessage}"
                            }, Status.FAILED)
                        });

                        await _unitOfWork.CommitAsync();
                    }
                    else
                    {
                        transfer.WUMsgId = messageId;
                        transfer.SMVValidationMessageId = string.IsNullOrWhiteSpace(smvValidationMessageId) ? string.Empty : smvValidationMessageId;
                        transfer.SMVValidationRemarks = string.IsNullOrWhiteSpace(smvValidationRemarks) ? string.Empty : smvValidationRemarks;
                        transfer.TempHostTxnId = string.IsNullOrWhiteSpace(tempHostTxnId) ? string.Empty : tempHostTxnId;
                        _logger.LogWarning($"Due to {wuTransferResult.ErrorMessage}, Transaction to RAK failed for {transfer.ReferenceNumber} and the beneficiary name " +
                            $":{transfer.MoneyTransferBeneficiary.FirstName} {transfer.MoneyTransferBeneficiary.LastName} ");

                        transfer.Status = Status.FAILED;
                        transfer.Remarks = wuTransferResult.ErrorMessage;
                        transfer.UpdatedDate = DateTime.Now;

                        // Add an entry to money transfer status progress.
                        transfer.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

                        // Hide any failed steps.
                        var failedSteps = transfer.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
                        foreach (var failedStep in failedSteps)
                        {
                            failedStep.Hide = true;
                        }

                        transfer.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                        {
                            Status = Status.FAILED,
                            Log = $"Transaction failed because of a RAK SendMoney API error.",
                            ProviderRemarks = wuTransferResult.ErrorMessage,
                            Message = ConstantParam.UM_TransferFailed
                        });

                        await _unitOfWork.CommitAsync();

                        await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                        {
                            new Tuple<string, MoneyTransferEvent, Status>(user.CardHolderId, new MoneyTransferEvent()
                            {
                                Amount = transfer.SendAmount,
                                BeneficiaryName = transfer.MoneyTransferBeneficiary.FirstName,
                                Country = transfer.MoneyTransferBeneficiary.Country.Code3,
                                ErrorMessage = wuTransferResult.ErrorMessage
                            }, transfer.Status)
                        });
                    }
                }

                if (hasApiCallFailed)
                {
                    return new ServiceResponse<SendMoneyTransferResultDto>(false, TransferStatusValidationMessage.RAKConnectionIssue.ToString());
                }

                // Add msg ID.
                transfer.WUMsgId = messageId;
                transfer.SMVValidationMessageId = string.IsNullOrWhiteSpace(smvValidationMessageId) ? string.Empty : smvValidationMessageId;
                transfer.SMVValidationRemarks = string.IsNullOrWhiteSpace(smvValidationRemarks) ? string.Empty : smvValidationRemarks;
                transfer.TempHostTxnId = string.IsNullOrWhiteSpace(tempHostTxnId) ? string.Empty : tempHostTxnId;
                // Create the external transaction in DB.
                transfer.CashPickupPin = wuTransferResult.Data.CashPickUpPin;
                transfer.CashPickUpPoint = wuTransferResult.Data.CashPickUpPoint;

                transfer.ReceiveAmount = TypeUtility.GetDecimalFromString(wuTransferResult.Data.TotalCreditAmount);

                if (!string.IsNullOrEmpty(transfer.Rate) && Convert.ToDecimal(transfer.Rate) > 0)
                {
                    transfer.ConversionRate = 1 / Convert.ToDecimal(transfer.Rate);
                }
                else
                {
                    transfer.ConversionRate = transfer.SendAmount / Convert.ToDecimal(wuTransferResult.Data.TotalCreditAmount);
                }

                transfer.ExternalTransaction = new MoneyTransferExternalTransaction
                {
                    ExternalStatus = wuTransferResult.Data.Status,
                    StatusDescription = wuTransferResult.Data.StatusDescription,
                    ExternalTransactionId = wuTransferResult.Data.ProviderTransferId,
                    StartDate = Convert.ToDateTime(wuTransferResult.Data.StartDate),
                    EndDate = Convert.ToDateTime(wuTransferResult.Data.EndDate),
                    Type = wuTransferResult.Data.TransactionType,
                    ChargesAmount = string.IsNullOrEmpty(wuTransferResult.Data.Charges.Amount) ? 0 : TypeUtility.GetDecimalFromString(wuTransferResult.Data.Charges.Amount),
                    ChargesCurrency = wuTransferResult.Data.Charges.Currency,
                    WaivedCharge = string.IsNullOrEmpty(wuTransferResult.Data.Charges.Discount) ? 0 : TypeUtility.GetDecimalFromString(wuTransferResult.Data.Charges.Discount),
                    TotalCharges = string.IsNullOrEmpty(wuTransferResult.Data.Charges.TotalAmount) ? 0 : TypeUtility.GetDecimalFromString(wuTransferResult.Data.Charges.TotalAmount),
                    TotalCreditAmount = string.IsNullOrEmpty(wuTransferResult.Data.TotalCreditAmount) ? 0 : TypeUtility.GetDecimalFromString(wuTransferResult.Data.TotalCreditAmount),
                    TotalDebitAmount = string.IsNullOrEmpty(wuTransferResult.Data.TotalDebitAmount) ? 0 : TypeUtility.GetDecimalFromString(wuTransferResult.Data.TotalDebitAmount),
                    CreditCurrency = string.IsNullOrEmpty(wuTransferResult.Data.CreditCurrency) ? ConstantParam.DefaultCurrency : wuTransferResult.Data.CreditCurrency,
                    DebitCurrency = string.IsNullOrEmpty(wuTransferResult.Data.DebitCurrency) ? ConstantParam.DefaultCurrency : wuTransferResult.Data.DebitCurrency
                };

                if (string.IsNullOrEmpty(wuTransferResult.Data.MsgId) == false)
                {
                    transfer.ExternalTransaction.MessageId = new Guid(wuTransferResult.Data.MsgId);
                }


                transfer.Transaction.StatusCode = "00";

                await _unitOfWork.CommitAsync();

                await CreateSpinTheWheel(transfer, user);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Connectivity Issue :{transfer.ReferenceNumber} && exception details {ex?.Message}");

                transfer.Status = Status.FAILED;
                transfer.Remarks = $"Connectivity Issue {ex?.Message}";
                // Add an entry to money transfer status progress.
                transfer.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();
                transfer.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
                {
                    Status = Status.FAILED,
                    Message = ConstantParam.UM_TransferFailed,
                    Log = $"Transfer failed because of connectivity issues when trying to send the transfer details to the messaging queue service. Exception details {ex?.Message}.",
                });

                // Call failed event.
                await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
                {
                    new Tuple<string, MoneyTransferEvent, Status>(user.CardHolderId, new MoneyTransferEvent()
                    {
                        Amount = transfer.SendAmount,
                        BeneficiaryName = beneficiary.FirstName,
                        Country = beneficiary.Country.Code3,
                        ErrorMessage = ex.Message
                    }, Status.FAILED)
                });

                await _unitOfWork.CommitAsync();
            }

            // Transfer Delays.
            // There are some cases where money transfer could be potentially delayed.
            // We won't show the delay popup if the transfer has failed, so we have to check the status.
            if (transfer.Status == Status.PENDING)
            {
                var country = await _unitOfWork.Countries.FirstOrDefaultAsync(z => z.Currency == transfer.ReceiveCurrency.ToUpper());
                var dateNow = DateTime.Now;

                var delay = await this._unitOfWork.MoneyTransferDelays.FirstOrDefaultAsync(d => d.IsActive
                && d.CountryCode == country.Code
                && d.StartDate <= dateNow
                && d.EndDate > dateNow, i => i.Country);

                if (delay != null)
                {
                    // We have found a delay.
                    response.MoneyTransferDelay = delay;
                }
            }

            try
            {
                var userIsEligibleForSalaryAdvanceCashBackResult = await this._salaryAdvanceCashBackService.UserIsEligibleForCashback(user);

                if (userIsEligibleForSalaryAdvanceCashBackResult.IsSuccessful)
                {
                    var userIsEligible = userIsEligibleForSalaryAdvanceCashBackResult.Data.Item1;

                    var cashBackAmount = userIsEligibleForSalaryAdvanceCashBackResult.Data.Item2;

                    if (userIsEligible)
                        await this._salaryAdvanceCashBackService.CreditCashBack(user, cashBackAmount);
                }
                else
                    _logger.LogWarning($"Check Salary Advance Cashback Eligibility Failed. Error: {userIsEligibleForSalaryAdvanceCashBackResult.ErrorMessage}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while trying to add Salary Advance Cashback on {transfer.ReferenceNumber} transaction with exception details {ex?.Message}");
            }

            response.MoneyTransferTransaction = transfer;

            response.TransferSummary = new Dictionary<string, string>
            {
                { "Date:", transfer.CreatedDate.ToString("dd MMMM yyyy") },
                { "Transfer Amount:", $"{transfer.SendAmount} AED" },
                { "Receive Amount:",$"{transfer.ReceiveAmount} {beneficiary.Country.Currency}" },
                { "Fee (Inclusive VAT):", $"{transfer.TotalCharges} AED" },
            };

            if (string.IsNullOrEmpty(transfer.Rate) == false)
            {
                response.TransferSummary.Add("Rate:", $"{transfer.Rate}");
            }
            else
            {
                var conversionRate = Math.Round(transfer.ConversionRate.Value, 7);
                response.TransferSummary.Add("Rate:", $"{Math.Round(1 / Convert.ToDecimal(conversionRate), 7)}");
            }

            var transferMethodSummary = string.Empty;
            switch (beneficiary.TransferType)
            {
                case MoneyTransferType.OutsideUAE:
                    transferMethodSummary = "Bank Transfer"; break;
                case MoneyTransferType.RAKMoneyCashPayout:
                    transferMethodSummary = "Cash Pickup"; break;
                case MoneyTransferType.Wallet:
                    transferMethodSummary = "Wallet"; break;
                default:
                    break;
            }

            response.TransferSummary.Add("Transfer Method:", $"{transferMethodSummary}");
            if (string.IsNullOrEmpty(transfer.CashPickupPin) == false)
            {
                var pin = Regex.Replace(transfer.CashPickupPin, ".{4}", "$0 ");
                response.TransferSummary.Add("Your PIN:", $"{pin}");
            }
            response.TransferSummary.Add("Total:", $"{transfer.ExternalTransaction.TotalDebitAmount} AED");


            // Push it to Queue (Only in case of pending)
            if (transfer.Status == Status.PENDING)
            {
                await EnqueueTransactionStatusUpdateAsync(transfer.Id, 1);
            }

            return new ServiceResponse<SendMoneyTransferResultDto>(response);
        }

        private async Task EnqueueTransactionStatusUpdateAsync(Guid transferId, int? enqueueDelayInMins = null)
        {
            try
            {
                await _messagingQueueService.SendAsync(new MoneyTransferMessageDto
                {
                    Id = transferId,
                    Action = MessageAction.Update
                }, this._rakSettings.MoneyTransferQueueConnectionString, this._rakSettings.MoneyTransferQueueName, enqueueDelayInMins);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while trying to push the transfer to the queue. Error: {ex?.Message}");
            }
        }

        private async Task<string> GetPlaceOfBirthAsync(CardHolder cardholder, string beneficiaryCountryCode)
        {
            if (!(await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableComplainceAdditionalFieldsForPAK)))
                return null;

            // Place of Birth (Only for PAK Transactions)
            if (cardholder != null && (cardholder.BirthDistrictId.HasValue || cardholder.BirthProvinceId.HasValue))
            {
                var provinceResult = await _unitOfWork.ProvinceRepository.GetProvinceAsync(cardholder.BirthProvinceId, cardholder.BirthDistrictId);
                if (provinceResult.IsSuccess)
                {
                    var district = provinceResult.Value.Districts?.FirstOrDefault()?.Name;
                    return string.IsNullOrWhiteSpace(district) ? provinceResult.Value.Name : $"{district}, {provinceResult.Value.Name}";
                }
            }
            return Country.DefaultProvince(beneficiaryCountryCode);
        }

        private async Task<string> GetTwoLetterNationalityCodeAsync(string threeLetterNationalityCode, string transactionCountryCode)
        {
            if (!(await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableComplainceAdditionalFieldsForPAK)))
                return null;
            if (transactionCountryCode == "PK")
            {
                var country = await _unitOfWork.Countries.FirstOrDefaultAsync(c => c.Code3 == threeLetterNationalityCode);
                if (country != null)
                    return country.Code;
            }
            return null;
        }

        private async Task<string> GetDateOfBirthAsync(CardHolder cardholder, string transactionCountryCode)
        {
            if (!(await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableComplainceAdditionalFieldsForPAK)))
                return null;
            if (transactionCountryCode == "PK")
            {
                if (cardholder?.Birthdate.HasValue == true && cardholder.Birthdate.Value > DateTime.MinValue && cardholder.Birthdate.Value < DateTime.MaxValue)
                    return cardholder.Birthdate.Value.ToString("yyyy-MM-dd");
            }
            return null;
        }

        private async Task SetMoneyTransferStatusAsStarted(MoneyTransferTransaction transfer, MoneyTransferBeneficiary beneficiary)
        {
            // Add an entry to money transfer status progress.
            transfer.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();
            transfer.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
            {
                Status = Status.STARTED,
                Message = ConstantParam.UM_TransferStarted,
                Log = "Status was set to started from SendMoney API.",
            });

            // Call started event.
            await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
            {
                new Tuple<string, MoneyTransferEvent, Status>(beneficiary.User.CardHolderId, new MoneyTransferEvent()
                {
                    Amount = transfer.SendAmount,
                    BeneficiaryName = beneficiary.FirstName,
                    Country = beneficiary.Country.Code3,
                    ErrorMessage = string.Empty
                }, Status.STARTED)
            });
        }

        private void SetMoneyTransferStatusAsPendingReverse(MoneyTransferTransaction transfer, string ex)
        {
            // Add an entry to money transfer status progress.
            transfer.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

            // Hide any failed steps.
            var failedSteps = transfer.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
            foreach (var failedStep in failedSteps)
            {
                failedStep.Hide = true;
            }

            transfer.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
            {
                Status = Status.PENDINGREVERSE,
                Log = $"Transaction failed because of a RAK SendMoney API exception.",
                ProviderRemarks = ex,
                Message = ConstantParam.UM_TransferFailedButWillBeReversed
            });
        }

        private async Task SetMoneyTransferStatusAsFailedAsync(MoneyTransferTransaction transfer, string ex)
        {
            // Add an entry to money transfer status progress.
            transfer.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();

            // Hide any failed steps.
            var failedSteps = transfer.MoneyTransferStatusSteps.Where(s => s.Status == Status.FAILED);
            foreach (var failedStep in failedSteps)
            {
                failedStep.Hide = true;
            }

            transfer.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
            {
                Status = Status.FAILED,
                Log = $"Transaction failed because of a RAK SendMoney API exception.",
                ProviderRemarks = ex.ToString(),
                Message = ConstantParam.UM_TransferFailed
            });

            // Call failed event.
            await _analyticsPublisherService.PublishMoneyTransferEvents(new List<Tuple<string, MoneyTransferEvent, Status>>()
            {
                new Tuple<string, MoneyTransferEvent, Status>(transfer.User.CardHolderId, new MoneyTransferEvent()
                {
                    Amount = transfer.SendAmount,
                    BeneficiaryName = transfer.MoneyTransferBeneficiary.FirstName,
                    Country = transfer.MoneyTransferBeneficiary.Country.Code3,
                    ErrorMessage = $"Transaction failed because of a RAK SendMoney API exception. Error: {ex}"
                }, Status.FAILED)
            });
        }

        public async Task<dynamic> GetRaffleTicket(Guid userId)
        {
            var thisEntry = await _unitOfWork.MoneyTransferRewardExperiment.FirstOrDefaultAsync(a => a.UserId == userId
                 && a.RewardType == MTRewardType.GoldCoinRaffle.ToString());
            if (thisEntry != null)
            {
                if (!thisEntry.IsClaimed)
                {
                    thisEntry.IsClaimed = true;
                    await _unitOfWork.CommitAsync();

                    return new MTReward()
                    {
                        TicketNumber = thisEntry.TicketNumber,
                        RaffleDate = _moneyTransferServiceSettings.RaffleDateString,
                        LastWinnerName = _moneyTransferServiceSettings.LastRaffleWinnerName,
                        LastWinnerTicketNumber = _moneyTransferServiceSettings.LastRaffleWinnerTicketNumber,
                    };
                }
            }

            return new MTReward()
            {
                TicketNumber = thisEntry != null ? thisEntry.TicketNumber : new Random().Next(100000, 999999).ToString(),
                RaffleDate = _moneyTransferServiceSettings.RaffleDateString,
                LastWinnerName = _moneyTransferServiceSettings.LastRaffleWinnerName,
                LastWinnerTicketNumber = _moneyTransferServiceSettings.LastRaffleWinnerTicketNumber,
            };
        }

        public async Task<dynamic> GetSpinTheWheel(Guid userId)
        {
            var thisEntry = await _unitOfWork.MoneyTransferRewardExperiment.FirstOrDefaultAsync(a => a.UserId == userId
            && a.RewardType == MTRewardType.SpinTheWheel.ToString());
            if (thisEntry != null)
            {
                if (!thisEntry.IsClaimed && thisEntry.RewardAmount > 0)
                {
                    thisEntry.IsClaimed = true;
                    await _unitOfWork.CommitAsync();

                    string title = string.Empty;
                    string description = string.Empty;
                    if (!string.IsNullOrEmpty(thisEntry.GifImageUrl))
                    {
                        if (thisEntry.GifImageUrl.ToLower().Contains("aed"))
                        {
                            title = string.Format("You've won AED {0}!", string.Format("{0:0.00}", thisEntry.RewardAmount));
                            description = "The amount will be credited to your account soon.";
                        }
                        if (thisEntry.GifImageUrl.ToLower().Contains("gold"))
                        {
                            title = string.Format("You won Gold Coin!");
                            description = "We will call you soon for the collection details.";
                        }
                    }
                    return new MTSpinTheWheel()
                    {
                        ImageUrl = thisEntry.GifImageUrl,
                        Title = title,
                        Description = description,
                    };
                }
            }
            return new MTSpinTheWheel()
            {
                ImageUrl = "https://cdn.edenred.ae/money-transfer/no_reward.gif",
                Title = "Better luck next time",
                Description = "Send money and get a chance to win next time.",
            };
        }

        public async Task<ServiceResponse<string>> GetDirectTransferReceiverLimitEligibility(Guid beneficiaryId, decimal amount, User user)
        {
            // Check user monthly limit.
            DateTime today = DateTime.Today;
            var monthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            var monthEnd = monthStart.AddMonths(1);

            var beneficiaryDetails = await _unitOfWork.MoneyTransferBeneficiaries.FirstOrDefaultAsync(x => x.Id == beneficiaryId);

            var linkedBeneficairies = new List<MoneyTransferBeneficiary>();

            if (beneficiaryDetails?.LinkedUserId != null)
            {
                linkedBeneficairies = await _unitOfWork.MoneyTransferBeneficiaries.GetLinkedBeneficiaries(beneficiaryDetails.LinkedUserId, MoneyTransferType.DirectTransfer);
            }
            else
            {
                linkedBeneficairies.Add(beneficiaryDetails);
            }

            var linkedBeneficiaryIds = linkedBeneficairies.Select(x => x.Id);

            var moneyTransferTransactions = await _unitOfWork.MoneyTransferTransactions.FindAsync(
                t => linkedBeneficiaryIds.Contains(t.MoneyTransferBeneficiaryId)
                && t.TransferType == BaseEnums.TransactionType.Direct
                && t.CreatedDate >= monthStart
                && t.CreatedDate <= monthEnd
                && (t.Status == Status.SUCCESSFUL || t.Status == Status.PENDING));

            var totalDirectMonthlyAmount = moneyTransferTransactions.Sum(transaction => transaction.SendAmount);

            if (totalDirectMonthlyAmount >= this._moneyTransferServiceSettings.DirectTransferReceiverMaxAmountPerMonth)
            {
                return new ServiceResponse<string>(DirectTransferEligbility.ReceiverLimitReached.ToString());
            }
            else if (totalDirectMonthlyAmount + amount > this._moneyTransferServiceSettings.DirectTransferReceiverMaxAmountPerMonth)
            {
                var beneficiary = await _unitOfWorkReadOnly.MoneyTransferBeneficiaries.FirstOrDefaultAsync(b => b.Id == beneficiaryId);

                if (beneficiary.LinkedUserId != null)
                {
                    var beneficiaryUser = await _unitOfWorkReadOnly.Users.FirstOrDefaultAsync(u => u.Id == beneficiary.LinkedUserId, z => z.CardHolder);

                    if (beneficiaryUser != null)
                    {
                        await _analyticsPublisherService.PublishDirectTransferSendLimitReachedEvent(beneficiaryUser.CardHolderId, new DirectTransferSendLimitReachedEvent()
                        {
                            Name = user.CardHolder.FirstName ?? string.Empty
                        });
                    }
                }
                return new ServiceResponse<string>(DirectTransferEligbility.SendAmountLimitReached.ToString());
            }
            else
                return new ServiceResponse<string>(DirectTransferEligbility.ValidAmount.ToString());
        }

        public static bool IsUserAllowedForSpinTheWheel(string CardHolderId, RewardServiceSettings _rewardServiceSettings, string userPhoneNumber)
        {
            string testAccountUsernames = string.IsNullOrEmpty(_rewardServiceSettings.TestAccountUsernames) ? string.Empty
            : new string(_rewardServiceSettings.TestAccountUsernames.Where(c => char.IsLetterOrDigit(c) || c == '|').ToArray());
            if (!string.IsNullOrEmpty(testAccountUsernames))
            {
                var phoneNumbers = testAccountUsernames.Split('|')
                                 .Where(p => p.StartsWith("00971"));
                if (phoneNumbers.Any())
                {
                    if (phoneNumbers.Contains(userPhoneNumber))
                        return true;
                    else
                        return false;
                }
            }

            var lowerBound = 80;
            var upperBound = 99;

            if (CardHolderId == null || CardHolderId.Length < 14)
            {
                return false;
            }

            string lastTwoDigits = CardHolderId.Substring(CardHolderId.Length - 2);

            if (!int.TryParse(lastTwoDigits, out int cardHolderLastTwoDigits))
            {
                return false;
            }
            // if card range is between 80 and 99, then user is not allowed for spin the wheel.
            return cardHolderLastTwoDigits < lowerBound || cardHolderLastTwoDigits > upperBound;
        }

        private async Task<(decimal? maxSendableAmount, InValidSendAmountError invalidAmountError)> CalculateMaxSendableAmount(
            decimal walletBalance,
            string countryCode,
            TransferMethod transferMethod,
            Guid userId,
            decimal requestedAmount)
        {
            try
            {
                // Get all fee ranges for this country and transfer method
                string cacheKey = $"{_lookupCachePrefix}TransferFees";
                var transferFees = await _cacheService.GetRecordAsync<IList<MoneyTransferFees>>(cacheKey);
                if (transferFees is null)
                {
                    transferFees = await _unitOfWork.MoneyTransferFees.FindAsync(f => f.IsActive);
                    await _cacheService.SetRecordAsync(cacheKey, transferFees, TimeSpan.FromDays(30));
                }

                var applicableFees = transferFees
                    .Where(x => x.CountryCode == countryCode && x.TransferMethod == transferMethod && x.IsActive)
                    .OrderBy(x => x.LowerLimit)
                    .ToList();

                if (!applicableFees.Any())
                {
                    return (null, null);
                }

                decimal maxSendableAmount = 0;

                // Check each fee range to find the maximum sendable amount
                foreach (var feeRange in applicableFees)
                {
                    decimal feeAmount = feeRange.Fees;
                    decimal rangeUpperLimit = feeRange.UpperLimit ?? decimal.MaxValue;

                    // Calculate max amount for this fee range
                    decimal maxAmountInRange = Math.Min(walletBalance - feeAmount, rangeUpperLimit);

                    // Ensure it's within the range bounds
                    if (maxAmountInRange >= feeRange.LowerLimit)
                        maxSendableAmount = Math.Max(maxSendableAmount, maxAmountInRange);
                }

                // Check if the requested amount exceeds what's possible
                if (requestedAmount > maxSendableAmount)
                {
                    // Check edge case: if balance - fees < 25 AED
                    if (walletBalance - applicableFees.First().Fees < 25)
                    {
                        return (null, new InValidSendAmountError
                        {
                            ErrorType = ErrorType.Warning.ToString(),
                            Title = "Your balance is low",
                            Description = "Your balance is low for sending the amount. Please try again later",
                            PrimaryCta = new PrimaryCta { Text = "Understood" },
                            SecondaryCta = null,
                            SuggestedAmountToSend = null
                        });
                    }

                    // Find the appropriate fee for the max sendable amount
                    var applicableFee = applicableFees.FirstOrDefault(x =>
                        maxSendableAmount >= x.LowerLimit &&
                        (x.UpperLimit == null || maxSendableAmount <= x.UpperLimit));

                    if (applicableFee != null)
                    {
                        return (maxSendableAmount, new InValidSendAmountError
                        {
                            ErrorType = ErrorType.Warning.ToString(),
                            Title = "Try Smaller Amount",
                            Description = $"You can send upto AED {maxSendableAmount:0.00} due to a small fee",
                            PrimaryCta = new PrimaryCta { Text = $"Send {maxSendableAmount:0.00}" },
                            SecondaryCta = new SecondaryCta { Text = "Cancel" },
                            SuggestedAmountToSend = maxSendableAmount
                        });
                    }
                }

                return (maxSendableAmount, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating max sendable amount");
                return (null, null);
            }
        }
    }
}

