using C3Pay.Core.Models.DTOs.MoneyTransfer;
using C3Pay.Core.Models;
using static C3Pay.Core.BaseEnums;
using FluentAssertions;
using Xunit;
using System;
using System.Collections.Generic;

namespace C3Pay.UnitTest.Tests.MoneyTransfer
{
    /// <summary>
    /// Business logic tests for ValidateTransfer InvalidSendAmountError scenarios
    /// These tests focus on the business rules and expected API responses
    /// </summary>
    public class ValidateTransferBusinessLogicTests
    {
        [Fact]
        public void ReviewTransferDto_WhenInvalidAmountErrorExists_ShouldReturn200WithCorrectStructure()
        {
            // Arrange
            var reviewTransferDto = new ReviewTransferDto
            {
                Successful = true,
                IsInValidSendAmountError = true,
                InValidSendAmountError = new InValidSendAmountError
                {
                    ErrorType = ErrorType.Warning.ToString(),
                    Title = "Try Smaller Amount",
                    Description = "You can send upto AED 864.25 due to a small fee",
                    PrimaryCta = new PrimaryCta { Text = "Send 864.25" },
                    SecondaryCta = new SecondaryCta { Text = "Cancel" },
                    SuggestedAmountToSend = 864.25m
                }
            };

            // Act & Assert - This represents a 200 OK response with error details
            reviewTransferDto.Successful.Should().BeTrue(); // API returns 200 OK
            reviewTransferDto.IsInValidSendAmountError.Should().BeTrue();
            reviewTransferDto.InValidSendAmountError.Should().NotBeNull();
            
            // Verify error structure matches requirements
            reviewTransferDto.InValidSendAmountError.ErrorType.Should().Be("Warning");
            reviewTransferDto.InValidSendAmountError.Title.Should().Be("Try Smaller Amount");
            reviewTransferDto.InValidSendAmountError.Description.Should().Be("You can send upto AED 864.25 due to a small fee");
            reviewTransferDto.InValidSendAmountError.PrimaryCta.Should().NotBeNull();
            reviewTransferDto.InValidSendAmountError.PrimaryCta.Text.Should().Be("Send 864.25");
            reviewTransferDto.InValidSendAmountError.SecondaryCta.Should().NotBeNull();
            reviewTransferDto.InValidSendAmountError.SecondaryCta.Text.Should().Be("Cancel");
            reviewTransferDto.InValidSendAmountError.SuggestedAmountToSend.Should().Be(864.25m);
        }

        [Fact]
        public void ReviewTransferDto_WhenLowBalanceError_ShouldReturn200WithCorrectErrorStructure()
        {
            // Arrange - Simulating the edge case where balance - fees < 25 AED
            var reviewTransferDto = new ReviewTransferDto
            {
                Successful = true,
                IsInValidSendAmountError = true,
                InValidSendAmountError = new InValidSendAmountError
                {
                    ErrorType = ErrorType.Warning.ToString(),
                    Title = "Your balance is low",
                    Description = "Your balance is low for sending the amount. Please try again later",
                    PrimaryCta = new PrimaryCta { Text = "Understood" },
                    SecondaryCta = null,
                    SuggestedAmountToSend = null
                }
            };

            // Act & Assert - This represents a 200 OK response with low balance error
            reviewTransferDto.Successful.Should().BeTrue(); // API returns 200 OK
            reviewTransferDto.IsInValidSendAmountError.Should().BeTrue();
            reviewTransferDto.InValidSendAmountError.Should().NotBeNull();
            
            // Verify error structure for low balance scenario
            reviewTransferDto.InValidSendAmountError.ErrorType.Should().Be("Warning");
            reviewTransferDto.InValidSendAmountError.Title.Should().Be("Your balance is low");
            reviewTransferDto.InValidSendAmountError.Description.Should().Be("Your balance is low for sending the amount. Please try again later");
            reviewTransferDto.InValidSendAmountError.PrimaryCta.Should().NotBeNull();
            reviewTransferDto.InValidSendAmountError.PrimaryCta.Text.Should().Be("Understood");
            reviewTransferDto.InValidSendAmountError.SecondaryCta.Should().BeNull();
            reviewTransferDto.InValidSendAmountError.SuggestedAmountToSend.Should().BeNull();
        }

        [Fact]
        public void ReviewTransferDto_WhenNoInvalidAmountError_ShouldReturn200WithoutError()
        {
            // Arrange - Normal successful validation
            var reviewTransferDto = new ReviewTransferDto
            {
                Successful = true,
                IsInValidSendAmountError = false,
                InValidSendAmountError = null
            };

            // Act & Assert - This represents a 200 OK response without any amount errors
            reviewTransferDto.Successful.Should().BeTrue(); // API returns 200 OK
            reviewTransferDto.IsInValidSendAmountError.Should().BeFalse();
            reviewTransferDto.InValidSendAmountError.Should().BeNull();
        }

        [Theory]
        [InlineData(1018, 1000, 15.75, "You can send upto AED 1000.00 due to a small fee")] // India scenario
        [InlineData(550, 500, 10.5, "You can send upto AED 500.00 due to a small fee")]     // Mid-range scenario
        [InlineData(200, 195, 5.25, "You can send upto AED 195.00 due to a small fee")]     // Low amount scenario
        public void InValidSendAmountError_ShouldCalculateCorrectMaxAmountBasedOnFeeRanges(
            decimal walletBalance, decimal expectedMaxAmount, decimal fee, string expectedDescription)
        {
            // Arrange - Simulating different fee range scenarios
            var invalidAmountError = new InValidSendAmountError
            {
                ErrorType = ErrorType.Warning.ToString(),
                Title = "Try Smaller Amount",
                Description = expectedDescription,
                PrimaryCta = new PrimaryCta { Text = $"Send {expectedMaxAmount:0.00}" },
                SecondaryCta = new SecondaryCta { Text = "Cancel" },
                SuggestedAmountToSend = expectedMaxAmount
            };

            // Act & Assert - Verify the business logic calculations
            invalidAmountError.ErrorType.Should().Be("Warning");
            invalidAmountError.Title.Should().Be("Try Smaller Amount");
            invalidAmountError.Description.Should().Be(expectedDescription);
            invalidAmountError.PrimaryCta.Text.Should().Be($"Send {expectedMaxAmount:0.00}");
            invalidAmountError.SecondaryCta.Text.Should().Be("Cancel");
            invalidAmountError.SuggestedAmountToSend.Should().Be(expectedMaxAmount);
        }

        [Fact]
        public void ErrorType_Enum_ShouldSupportBothWarningAndError()
        {
            // Arrange & Act & Assert
            ErrorType.Warning.ToString().Should().Be("Warning");
            ErrorType.Error.ToString().Should().Be("Error");
            
            // Verify enum can be used in business logic
            var warningError = new InValidSendAmountError { ErrorType = ErrorType.Warning.ToString() };
            var criticalError = new InValidSendAmountError { ErrorType = ErrorType.Error.ToString() };
            
            warningError.ErrorType.Should().Be("Warning");
            criticalError.ErrorType.Should().Be("Error");
        }

        [Fact]
        public void ReviewTransferDto_ShouldMaintainBackwardCompatibility()
        {
            // Arrange - Ensure existing properties still work
            var reviewTransferDto = new ReviewTransferDto
            {
                Successful = true,
                Error = null,
                IsFirstTransfer = true,
                IsFirstTransferForBeneficiary = false,
                BeneficarySummary = new Dictionary<string, string> { { "Name", "John Doe" } },
                TransferSummary = new Dictionary<string, string> { { "Amount", "1000 AED" } },
                IsSmvApiCalled = false,
                IsSmvValidationSuccess = true,
                SmvValidationError = null,
                LogFxRate = "1.0",
                // New properties
                IsInValidSendAmountError = true,
                InValidSendAmountError = new InValidSendAmountError
                {
                    ErrorType = ErrorType.Warning.ToString(),
                    Title = "Test",
                    Description = "Test Description"
                }
            };

            // Act & Assert - Verify all properties work together
            reviewTransferDto.Successful.Should().BeTrue();
            reviewTransferDto.IsFirstTransfer.Should().BeTrue();
            reviewTransferDto.BeneficarySummary.Should().ContainKey("Name");
            reviewTransferDto.TransferSummary.Should().ContainKey("Amount");
            reviewTransferDto.IsSmvApiCalled.Should().BeFalse();
            reviewTransferDto.IsSmvValidationSuccess.Should().BeTrue();
            
            // New properties should also work
            reviewTransferDto.IsInValidSendAmountError.Should().BeTrue();
            reviewTransferDto.InValidSendAmountError.Should().NotBeNull();
            reviewTransferDto.InValidSendAmountError.ErrorType.Should().Be("Warning");
        }

        [Fact]
        public void API_Response_ShouldAlwaysReturn200OK_WhenInvalidAmountErrorExists()
        {
            // This test documents the expected API behavior:
            // Even when there's an InvalidSendAmountError, the API should return 200 OK
            // with the error details in the response body, not as an HTTP error status
            
            // Arrange
            var apiResponse = new ServiceResponse<ReviewTransferDto>(new ReviewTransferDto
            {
                Successful = true, // This indicates 200 OK
                IsInValidSendAmountError = true,
                InValidSendAmountError = new InValidSendAmountError
                {
                    ErrorType = ErrorType.Warning.ToString(),
                    Title = "Try Smaller Amount",
                    Description = "You can send upto AED 864.25 due to a small fee"
                }
            });

            // Act & Assert
            apiResponse.IsSuccessful.Should().BeTrue(); // HTTP 200 OK
            apiResponse.Data.Should().NotBeNull();
            apiResponse.Data.Successful.Should().BeTrue(); // Business operation successful
            apiResponse.Data.IsInValidSendAmountError.Should().BeTrue(); // But with amount validation warning
            apiResponse.Data.InValidSendAmountError.Should().NotBeNull();
        }
    }
}
