﻿using System;
using System.Collections.Generic;
using System.Text;

namespace C3Pay.Core.Models.DTOs.MoneyTransfer
{
    public class ReviewTransferDto
    {
        public static string TECHNICAL_ERROR_CODE = "20037";
        public bool Successful { get; set; }
        public ErrorInformation Error { get; set; }
        public bool IsFirstTransfer { get; set; }
        public bool IsFirstTransferForBeneficiary { get; set; }
        public Dictionary<string, string> BeneficarySummary { get; set; }
        public Dictionary<string, string> TransferSummary { get; set; }

        public bool IsSmvApiCalled { get; set; }
        public bool IsSmvValidationSuccess { get; set; }
        public SmvValidationError SmvValidationError { get; set; }

        public bool IsInValidSendAmountError { get; set; }
        public InValidSendAmountError InValidSendAmountError { get; set; }

        // Capture the displayed FX rate to the UI.
        public string LogFxRate { get; set; }

        public void Set(bool isSmvApiCalled, bool isSmvValidationSuccess, string errorMessage)
        {
            IsSmvApiCalled = isSmvApiCalled;
            IsSmvValidationSuccess = isSmvValidationSuccess;
            if (!string.IsNullOrEmpty(errorMessage))
            {
                string errorCode = TECHNICAL_ERROR_CODE; // Defaulting to technical error
                if (errorMessage.Contains("-") && errorMessage.Split('-').Length > 0)
                    errorCode = errorMessage.Split('-')[0].Trim();
                SetSMVValidationError(errorCode);

                // ###TODO: If Error is technical, then setting IsSmvValidationSuccess to true (Need to remove this once FE handles this)
                if (SmvValidationError != null && SmvValidationError.PrimaryCta != null && SmvValidationError.PrimaryCta.ActionType == SMVActionType.Technical.ToString())
                    IsSmvValidationSuccess = true;
            }

        }
        private void SetSMVValidationError(string errorCode)
        {
            var errorMappings = new Dictionary<string, (string Title, string Description, SMVActionType ActionType)>
            {
                // Beneficiary Details related errors
                ["20027"] = ("Name may not be correct!", "Please make sure the name matches the account name. Please check and try again.", SMVActionType.Beneficiary),
                ["20028"] = ("Account number may not be correct!", "Receiver account number may not be correct. Please check and enter correct details.", SMVActionType.Beneficiary),
                ["20029"] = ("Account number may not be correct!", "Receiver account number may not be correct. Please check and enter correct details.", SMVActionType.Beneficiary),
                ["20031"] = ("Receiver account may not be active!", "Please check the details and try again.", SMVActionType.Beneficiary),
                ["20032"] = ("Mobile number may not be correct!", "Receiver number is not linked with the wallet. Please enter the correct Number.", SMVActionType.Beneficiary),
                ["20033"] = ("You can only send to personal account!", "You cannot send money to Buisness account. Please send money to different receiver.", SMVActionType.Beneficiary),

                // Transaction limit related errors
                ["20030"] = ("Transfer limit is reached!", "Please retry with less money.", SMVActionType.Transaction),
                ["20034"] = ("Transfer limit is reached!", "Please retry with less money.", SMVActionType.Transaction),

                // Technical related errors
                ["20035"] = (null, null, SMVActionType.Technical),
                ["20036"] = (null, null, SMVActionType.Technical),
                ["20037"] = (null, null, SMVActionType.Technical)
            };

            // Try to get error details from the dictionary
            if (!errorMappings.TryGetValue(errorCode, out var errorDetails))
            {
                // By default return technical error type
                errorDetails = (null, null, SMVActionType.Technical);
            }

            var (title, description, actionType) = errorDetails;

            SmvValidationError = new SmvValidationError()
            {
                Title = title,
                Description = description,
                PrimaryCta = new Cta
                {
                    ActionType = actionType.ToString(),
                    Text = actionType == SMVActionType.Beneficiary ? "Add New Receiver" :
                           actionType == SMVActionType.Transaction ? "Understood" : null
                }
            };
        }


    }

    public class SmvValidationError
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public Cta PrimaryCta { get; set; }
    }

    public class Cta
    {
        public string Text { get; set; }
        public string ActionType { get; set; }
    }

    public class InValidSendAmountError
    {
        public string ErrorType { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public PrimaryCta PrimaryCta { get; set; }
        public SecondaryCta SecondaryCta { get; set; }
        public decimal? SuggestedAmountToSend { get; set; }
    }

    public class PrimaryCta
    {
        public string Text { get; set; }
    }

    public class SecondaryCta
    {
        public string Text { get; set; }
    }

    public enum ErrorType
    {
        Warning,
        Error
    }
}
